package org.technoserve.udp.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.oidc.OidcIdToken;
import org.springframework.security.oauth2.core.oidc.user.DefaultOidcUser;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.technoserve.udp.service.CustomUserRoleService;

import java.util.Collection;


public class CustomJwtAuthenticationConverter implements Converter<Jwt, AbstractAuthenticationToken> {

  private final JwtGrantedAuthoritiesConverter delegate = new JwtGrantedAuthoritiesConverter();

  private static final Logger logger = LoggerFactory.getLogger(CustomJwtAuthenticationConverter.class);

  @Autowired
  private CustomUserRoleService userRoleService;

  @Override

  public AbstractAuthenticationToken convert(Jwt jwt) {
    // Extract default authorities from the JWT
    //Collection<GrantedAuthority> authorities = delegate.convert(jwt);


    // Construct an OIDC ID token from the JWT claims
    OidcIdToken idToken = new OidcIdToken(jwt.getTokenValue(), jwt.getIssuedAt(), jwt.getExpiresAt(), jwt.getClaims());

    //logger.info(jwt.getSubject());
    // Create an OIDC user with the obtained authorities and ID token.
    // Here we use "email" as the userNameAttribute; adjust as needed.
    Collection<? extends GrantedAuthority> authorityList =  userRoleService.getAuthorities(jwt.getClaims().get("email").toString());

    OidcUser oidcUser = new DefaultOidcUser(authorityList, idToken, "sub");
   // logger.info(authorityList.toString());
    // Wrap the OIDC user in an OAuth2AuthenticationToken.
    // The third parameter ("okta") should match your client registration ID.
    return new OAuth2AuthenticationToken(oidcUser, authorityList, "google");


  }
}
