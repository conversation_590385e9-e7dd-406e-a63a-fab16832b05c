package org.technoserve.udp.security;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;
import org.technoserve.udp.entity.auth.EndPoint;
import org.technoserve.udp.entity.auth.Role;
import org.technoserve.udp.entity.screens.Permissions;
import org.technoserve.udp.entity.screens.Screen;
import org.technoserve.udp.repository.RoleRepository;
import org.technoserve.udp.service.RoleService;

import java.util.ArrayList;
import java.util.List;

@Component("customEndpointAuth")
@RequiredArgsConstructor
public class CustomEndpointAuthorization {

    private final RoleRepository roleRepository;

    private final RoleService roleService;

    /**
     * Checks if the current authenticated user has access to the requested endpoint.
     *
     * @param request        the HttpServletRequest to obtain URL and HTTP method
     * @param authentication the current Authentication (which should include roles)
     * @return true if access is permitted; false otherwise
     */

    public boolean hasAccess(HttpServletRequest request, Authentication authentication) {

        for (GrantedAuthority authority : authentication.getAuthorities()) {
            Role role = roleService.getRoleByName(authority.getAuthority());
            List<Screen> screens = new ArrayList<>(role.getScreens());
            for (Screen screen : screens) {
                List<Permissions> permissions = new ArrayList<>(screen.getEnabledPermissions());
                for (Permissions permission : permissions) {
                    List<EndPoint> endpoints = new ArrayList<>(permission.getEndPoints());
                    for (EndPoint endpoint : endpoints) {
                        if (new AntPathRequestMatcher(endpoint.getPattern(), endpoint.getHttpMethod()).matches(request)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
}
