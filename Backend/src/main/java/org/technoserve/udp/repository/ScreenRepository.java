package org.technoserve.udp.repository;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.technoserve.udp.entity.auth.Role;
import org.technoserve.udp.entity.screens.Screen;

public interface ScreenRepository extends JpaRepository<Screen,Long> {

  @Transactional
  @Modifying
  @Query("DELETE FROM Screen s WHERE s.role = :role")
  void deleteScreenByRole(@Param("role") Role role);

}
