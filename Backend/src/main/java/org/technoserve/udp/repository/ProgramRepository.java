package org.technoserve.udp.repository;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.Program;

import java.util.List;

@Repository
public interface ProgramRepository extends JpaRepository<Program, Long> {

  List<Program> findByValueChain_ValueChainIdAndStatusNot(Long valueChainId, Status status);

  boolean existsByNameAndValueChain_ValueChainIdAndStatusNot(@NotBlank(message = "Name is required") String name, @NotNull Long valueChainId, Status status);

  boolean existsByNameAndValueChain_ValueChainIdAndStatusNotAndProgramIdNot(@NotBlank(message = "Name is required") String name, @NotNull Long valueChainId, Status status, Long programId);


}