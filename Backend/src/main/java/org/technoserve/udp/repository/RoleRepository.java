package org.technoserve.udp.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.technoserve.udp.entity.auth.Role;
import org.technoserve.udp.entity.auth.UserEntity;

import java.util.List;
import java.util.Optional;

public interface RoleRepository extends JpaRepository<Role, Long> {

    //  Optional<Role> findByName(String name);
    @Query("SELECT r FROM Role r LEFT JOIN FETCH r.screens WHERE r.name = :name")
    Optional<Role> findByName(@Param("name") String name);

    // Find roles with a name filter and excluding ADMIN
    Page<Role> findByNameContainingIgnoreCaseAndNameNot(String name, String excludeRole, Pageable pageable);

    // Find all roles excluding ADMIN
    Page<Role> findByNameNot(String excludeRole, Pageable pageable);
    List<Role> findByNameIn(List<String> names);
}
