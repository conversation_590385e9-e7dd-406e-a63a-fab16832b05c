package org.technoserve.udp.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.dataflow.FileType;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;

@Repository
public interface ExcelFileMetaDataRepository extends JpaRepository<ExcelFileMetaData, Long> {

    /**
     * Find Excel file metadata by program, partner, and entity type
     *
     * @param program The program
     * @param partner The partner
     * @param entityType The entity type
     * @return List of Excel file metadata
     */
    Page<ExcelFileMetaData> findByProgramAndPartnerAndFileTypeAndEntityType(
        Program program,
        Partner partner,
        FileType fileType,
        String entityType,
        Pageable pageable
    );
}
