package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.technoserve.udp.entity.common.MasterDistrict;
import org.technoserve.udp.entity.common.MasterState;

import java.util.List;
import java.util.Optional;

public interface MasterDistrictRepository extends JpaRepository<MasterDistrict, Long> {
  List<MasterDistrict> findByMasterState_MasterStateIdOrderByDistrictNameAsc(Long masterStateId);

  Optional<MasterDistrict> findByDistrictNameIgnoreCaseAndMasterState(String districtName, MasterState masterState);
}
