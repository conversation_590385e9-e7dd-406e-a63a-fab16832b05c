package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.DairyFieldData;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface DairyFieldDataRepository extends JpaRepository<DairyFieldData, Long> {

    Optional<DairyFieldData> findByDateAndProgramIdAndPartnerId(LocalDate date, Long programId, Long partnerId);

    void deleteAllByExcelFileMetaData_ExcelFileMetaDataId(Long excelFileMetaDataId);

    /**
     * Find the latest dairy field data by program ID, partner ID, and date range
     *
     * @param programId The program ID (optional)
     * @param partnerId The partner ID (optional)
     * @param startDate The start date (optional)
     * @param endDate The end date (optional)
     * @return The latest dairy field data within the date range
     */
    @Query("SELECT f FROM DairyFieldData f WHERE "
        + "(:programId IS NULL OR f.programId = :programId) AND "
        + "(:partnerId IS NULL OR f.partnerId = :partnerId) AND "
        + "f.date BETWEEN :startDate AND :endDate "
        + "ORDER BY f.date DESC")
    List<DairyFieldData> findLatestByProgramIdAndPartnerIdAndDateRange(
        @Param("programId") Long programId,
        @Param("partnerId") Long partnerId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate);
}
