package org.technoserve.udp.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.auth.UserEntity;
import org.technoserve.udp.entity.auth.UserStatus;

import java.util.Optional;
import java.util.Set;

@Repository
public interface UserRepository extends JpaRepository<UserEntity,String> {
    boolean existsByEmail(String email);

    @EntityGraph(attributePaths = {"roles"})
    @Query("SELECT u FROM UserEntity u WHERE u.email = :email")
    Optional<UserEntity> findByEmailWithRoles(@Param("email") String email);

    Set<UserEntity> findByRoles_Name(String roleName);

    Page<UserEntity> findByEmailContainingIgnoreCaseAndStatusAndRoles_NameNot(
            String email, UserStatus status, String roleName, Pageable pageable
    );





}
