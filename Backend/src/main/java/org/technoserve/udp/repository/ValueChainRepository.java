package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.valuechain.ValueChain;

import java.util.List;
import java.util.Optional;

@Repository
public interface ValueChainRepository extends JpaRepository<ValueChain,Long> {

  boolean existsByName(String name);

  boolean existsByNameAndStatus(String name, Status status);

  Optional<ValueChain> findByValueChainIdAndStatus(Long id, Status status);

  List<ValueChain> findAllByStatus(Status status);
}
