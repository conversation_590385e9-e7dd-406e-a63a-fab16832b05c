package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.ColumnMappingTemplate;
import org.technoserve.udp.entity.dataflow.FileType;

import java.util.List;

@Repository
public interface ColumnMappingTemplateRepository extends JpaRepository<ColumnMappingTemplate, Long> {

  List<ColumnMappingTemplate> findByProgramIdAndPartnerIdAndFileTypeAndEntityType(Long programId, Long partnerId, FileType fileType, String entityType);

}
