package org.technoserve.udp.repository;

import jakarta.validation.constraints.NotBlank;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.sponsor.Sponsor;

import java.util.Collection;
import java.util.List;

@Repository
public interface SponsorRepository extends JpaRepository<Sponsor,Long> {

  List<Sponsor> findAllByStatus(Status status);

  boolean existsByNameAndStatus(@NotBlank(message = "Sponsor Name is required") String name, Status status);
}
