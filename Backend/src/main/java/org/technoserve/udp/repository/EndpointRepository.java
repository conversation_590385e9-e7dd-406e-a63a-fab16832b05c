package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.technoserve.udp.entity.auth.EndPoint;

import java.util.List;

public interface EndpointRepository extends JpaRepository<EndPoint, Long> {
  // Optionally, find endpoints by HTTP method or pattern if needed.
  List<EndPoint> findByHttpMethodIgnoreCase(String httpMethod);


}