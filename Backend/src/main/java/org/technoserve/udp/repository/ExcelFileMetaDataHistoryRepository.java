package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaDataHistory;

import java.util.List;

@Repository
public interface ExcelFileMetaDataHistoryRepository extends JpaRepository<ExcelFileMetaDataHistory, Long> {
    
    /**
     * Find history records by Excel file metadata ID
     * 
     * @param excelFileMetaDataId The Excel file metadata ID
     * @return List of history records
     */
    List<ExcelFileMetaDataHistory> findByExcelFileMetaDataIdOrderByArchivedOnDesc(Long excelFileMetaDataId);
}
