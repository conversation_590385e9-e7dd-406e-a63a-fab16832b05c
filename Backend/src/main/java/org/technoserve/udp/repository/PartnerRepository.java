package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.Partner;

import java.util.Collection;
import java.util.List;

@Repository
public interface PartnerRepository extends JpaRepository<Partner,Long> {

  List<Partner> findAllByStatus(Status status);
  boolean existsByNameAndStatus(String name, Status status);

  /**
   * Find all active partners by program ID
   *
   * @param programId The program ID
   * @return List of partners
   */
  @Query("SELECT p FROM Program prog " +
      "JOIN prog.programFactSheet pfs " +
      "JOIN pfs.projectPartners p " +
      "WHERE prog.programId = :programId AND p.status = 'CREATED'")
  List<Partner> findAllActivePartnersByProgramId(@Param("programId") Long programId);
}
