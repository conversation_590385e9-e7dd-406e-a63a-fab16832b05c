package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.FundCodeEntity;

import java.util.List;

@Repository
public interface FundCodeRepository extends JpaRepository<FundCodeEntity,Long> {

  boolean existsByFundCodeAndProgram_ProgramIdAndStatus(String fundCode, Long programId, Status status);

  List<FundCodeEntity> findByProgramProgramIdAndStatus(Long programId, Status status);
}