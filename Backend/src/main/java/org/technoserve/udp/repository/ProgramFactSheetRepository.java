package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.entity.program.ProgramFactSheet;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface ProgramFactSheetRepository extends JpaRepository<ProgramFactSheet, Long> {

  boolean existsByProgram(Program program);

  Optional<ProgramFactSheet> findByProgram_ProgramId(Long programId);

  List<ProgramFactSheet> findByStartYearAndProgram_Status(LocalDate today, Status status);

  List<ProgramFactSheet> findByEndYear(LocalDate yesterday);
}
