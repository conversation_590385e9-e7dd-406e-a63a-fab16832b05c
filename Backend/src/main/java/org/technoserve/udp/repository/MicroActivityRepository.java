package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.technoserve.udp.entity.program.MicroActivity;
import java.util.List;

public interface MicroActivityRepository extends JpaRepository<MicroActivity, Long> {

  @Query("SELECT ma FROM MicroActivity ma JOIN ma.subActivity sa JOIN sa.keyActivity ka JOIN ka.outputIndicator oi " +
      "JOIN oi.objective obj JOIN obj.dimension dim JOIN dim.programLogFrame plf " +
      "WHERE plf.program.programId = :programId AND ma.microActivityType = 'FINANCIAL' and ma.status = 'CREATED'")
  List<MicroActivity> findAllByProgramIdAndTypeFinancial(@Param("programId") Long programId);

}


