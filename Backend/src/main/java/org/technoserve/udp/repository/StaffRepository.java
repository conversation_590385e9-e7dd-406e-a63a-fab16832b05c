package org.technoserve.udp.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.Staff;
import org.technoserve.udp.entity.dataflow.StaffId;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;

import java.util.Optional;

@Repository
public interface StaffRepository extends JpaRepository<Staff, StaffId> {

    Optional<Staff> findByStaffIdAndProgramIdAndPartnerId(String staffId, Long programId, Long partnerId);

    void deleteAllByExcelFileMetaData_ExcelFileMetaDataId(Long excelFileMetaDataId);

    /**
     * Find staff by program ID and partner ID with pagination
     *
     * @param programId The program ID (optional)
     * @param partnerId The partner ID (optional)
     * @param pageable Pagination information
     * @return Page of staff
     */
    @Query("SELECT s FROM Staff s WHERE "
        + "( s.programId = :programId) AND "
        + "(:partnerId IS NULL OR s.partnerId = :partnerId)")
    Page<Staff> findByProgramIdAndPartnerId(
        @Param("programId") Long programId,
        @Param("partnerId") Long partnerId,
        Pageable pageable);
}
