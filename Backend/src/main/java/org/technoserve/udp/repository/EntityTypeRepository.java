package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.technoserve.udp.entity.dataflow.EntityTypeMaster;
import org.technoserve.udp.entity.dataflow.FileType;

import java.util.List;

public interface EntityTypeRepository extends JpaRepository<EntityTypeMaster, String> {
  List<EntityTypeMaster> findByFileTypeAndValueChainType(FileType fileType, String valueChainType);
}
