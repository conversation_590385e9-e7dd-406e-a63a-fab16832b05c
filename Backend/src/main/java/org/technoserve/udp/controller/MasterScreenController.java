package org.technoserve.udp.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.technoserve.udp.dto.MasterScreenDto;
import org.technoserve.udp.service.MasterScreenService;

import java.util.List;

/**
 * Controller for managing master screens.
 */
@Slf4j // Enables logging
@RestController
@RequestMapping("/master-screens")
@RequiredArgsConstructor // Auto-generates constructor injection
public class MasterScreenController {

    private final MasterScreenService masterScreenService;

    /**
     * Fetches all master screens.
     *
     * @return List of MasterScreenDto objects
     */
    @GetMapping
    public List<MasterScreenDto> getMasterData() {
        log.info("Fetching master screen data...");

        List<MasterScreenDto> masterScreens = masterScreenService.getMasterData();

        log.info("Retrieved {} master screens.", masterScreens.size());
        return masterScreens;
    }
}
