package org.technoserve.udp.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.technoserve.udp.dto.*;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaDataHistory;
import org.technoserve.udp.entity.dataflow.FileType;
import org.technoserve.udp.exception.BadRequestException;
import org.technoserve.udp.service.DataFlowService;
import org.technoserve.udp.service.EntityTypeService;
import org.technoserve.udp.service.GcpCloudServices;
import org.technoserve.udp.util.UdpCommonUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RequestMapping("/dataflow")
@Controller
@RequiredArgsConstructor
public class DataFlowController {

  private final DataFlowService dataFlowService;

  private final GcpCloudServices gcpCloudServices;

  private final EntityTypeService entityTypeService;

  @PostMapping("/upload-excel")
  public ResponseEntity<Map<String, Object>> uploadExcelFile(
      @RequestParam("file") MultipartFile file,
      @RequestParam("fileType") String fileType,
      @RequestParam("programId") Long programId,
      @RequestParam("partnerId") Long partnerId,
      @RequestParam("entityType") String entityType
      ) {
    return ResponseEntity.ok(dataFlowService.uploadExcelAndSaveMeta(file, fileType, programId, partnerId, entityType));
  }

  @GetMapping("/entity-fields")
  public ResponseEntity<List<FieldInfoResponse>> getEntityFields(@RequestParam String entityType) throws ClassNotFoundException {
    return ResponseEntity.ok(UdpCommonUtil.getEntityFields(entityType));
  }

  @GetMapping("/excel-headers/{excelFileMetaDataId}")
  public ResponseEntity<DataflowColumnMappingResponse> getExcelHeaders(@PathVariable Long excelFileMetaDataId) {
    return ResponseEntity.ok(dataFlowService.getExcelHeaders(excelFileMetaDataId));
  }

  /**
   * Validate and process an Excel file in parallel in a single operation
   * First validates the file and then processes it if validation passes
   * This method uses multi-threading for faster validation and processing of large files
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @return ValidateAndProcessResponse with validation and processing results
   */
  @PostMapping("/validate-and-process/{excelFileMetaDataId}")
  public ResponseEntity<ValidateAndProcessResponse> validateAndProcessExcelFile(
      @PathVariable Long excelFileMetaDataId) {

    ValidateAndProcessResponse response = dataFlowService.validateAndProcessExcelFileInParallel(excelFileMetaDataId);
    return ResponseEntity.ok(response);
  }

  /**
   * Save or update column mappings for a specific program, partner, and entity type
   *
   * @param programId The program ID
   * @param partnerId The partner ID
   * @param fileType The file type
   * @param entityType The entity type
   * @param mappings List of column mappings with excelColumn and entityField
   * @return ApiResponse with success message and count of mappings saved
   */
  @PostMapping("/column-mappings")
  public ResponseEntity<ApiResponse> saveOrUpdateColumnMappings(
      @RequestParam Long programId,
      @RequestParam Long partnerId,
      @RequestParam String fileType,
      @RequestParam String entityType,
      @RequestBody List<ColumnMappingDto> mappings) {

    ApiResponse response = dataFlowService.saveOrUpdateColumnMappings(programId, partnerId, fileType, entityType, mappings);
    return ResponseEntity.ok(response);
  }

  /**
   * Get Excel file metadata by program ID, partner ID, and entity type
   * All parameters are optional and can be used for filtering
   *
   * @param programId The program ID (optional)
   * @param partnerId The partner ID (optional)
   * @param entityType The entity type (optional)
   * @return List of Excel file metadata responses
   */
  @GetMapping("/excel-files")
  public ResponseEntity<Page<ExcelFileMetaDataResponse>> getExcelFileMetaData(
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) Long partnerId,
      @RequestParam(required = false) String fileType,
      @RequestParam(required = false) String entityType,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "uploadedOn") String sortBy,
      @RequestParam(defaultValue = "desc") String sortDir
  ) {

    // Create a Sort object dynamically
    Sort sort = Sort.by(sortBy);
    sort = sortDir.equalsIgnoreCase("asc") ? sort.ascending() : sort.descending();

    Pageable pageable = PageRequest.of(page, size, sort);
    Page<ExcelFileMetaDataResponse> response = dataFlowService.getExcelFileMetaData(
        programId, partnerId, fileType, entityType, pageable);
    return ResponseEntity.ok(response);
  }

  @GetMapping("/download")
  public ResponseEntity<byte[]> downloadExcelFile(@RequestParam String path) {
    byte[] fileContent = gcpCloudServices.downloadExcelFile(path);

    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + path.substring(path.lastIndexOf('/') + 1));
    headers.add(HttpHeaders.CONTENT_TYPE, "application/octet-stream");

    return ResponseEntity
        .ok()
        .headers(headers)
        .body(fileContent);
  }

  @GetMapping("/file-types")
  public ResponseEntity<List<EnumResponse>> getFileTypes() {
    List<EnumResponse> fileTypes = Arrays.stream(FileType.values())
        .map(fileType -> new EnumResponse(fileType.name(), fileType.getDisplayValue()))
        .toList();
    return ResponseEntity.ok(fileTypes);
  }

  @GetMapping("/entity-types")
  public ResponseEntity<List<EnumResponse>> getEntityTypes(@RequestParam FileType fileType,@RequestParam Long valueChainId){
    if(fileType == null || valueChainId == null){
      throw new BadRequestException("Invalid params");
    }
    return ResponseEntity.ok(entityTypeService.getEntityTypes( fileType, valueChainId));
  }

  /**
   * Reupload an Excel file for a specific Excel file metadata ID
   * This will delete all existing data related to the Excel file and reset the status to UPLOADED
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @param file The new Excel file to upload
   * @return ApiResponse with success message and the Excel file metadata ID
   */
  @PutMapping("/reupload-excel/{excelFileMetaDataId}")
  public ResponseEntity<Map<String,Object>> reuploadExcelFile(
      @PathVariable Long excelFileMetaDataId,
      @RequestParam("file") MultipartFile file) {

    return ResponseEntity.ok(dataFlowService.reuploadExcelFile(excelFileMetaDataId, file));
  }

  /**
   * Get the history of an Excel file metadata
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @return List of Excel file metadata history records
   */
  @GetMapping("/excel-files/{excelFileMetaDataId}/history")
  public ResponseEntity<List<ExcelFileMetaDataHistoryResponse>> getExcelFileMetaDataHistory(
      @PathVariable Long excelFileMetaDataId) {

    List<ExcelFileMetaDataHistory> historyList = dataFlowService.getExcelFileMetaDataHistory(excelFileMetaDataId);

    // Convert to response DTOs
    List<ExcelFileMetaDataHistoryResponse> responseList = historyList.stream()
        .map(history -> ExcelFileMetaDataHistoryResponse.builder()
            .historyId(history.getHistoryId())
            .excelFileMetaDataId(history.getExcelFileMetaDataId())
            .fileName(history.getFileName())
            .fileType(history.getFileType())
            .entityType(history.getEntityType())
            .path(history.getPath())
            .validationResultPath(history.getValidationResultPath())
            .uploadStatus(history.getUploadStatus())
            .uploadedOn(history.getUploadedOn())
            .uploadedBy(history.getUploadedBy())
            .processedOn(history.getProcessedOn())
            .processedBy(history.getProcessedBy())
            .archivedOn(history.getArchivedOn())
            .archivedBy(history.getArchivedBy())
            .build())
        .toList();

    return ResponseEntity.ok(responseList);
  }

  /**
   * Soft delete an Excel file metadata and delete related data
   * This will delete all related data and mark the Excel file metadata as DELETED
   *
   * @param excelFileMetaDataId The ID of the Excel file metadata
   * @return ApiResponse with success message
   */
  @DeleteMapping("/excel-files/{excelFileMetaDataId}")
  public ResponseEntity<ApiResponse> softDeleteExcelFile(
      @PathVariable Long excelFileMetaDataId) {

    ApiResponse apiResponse = dataFlowService.softDeleteExcelFile(excelFileMetaDataId);
    return ResponseEntity.ok(apiResponse);
  }
}
