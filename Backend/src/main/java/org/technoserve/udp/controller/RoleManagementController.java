package org.technoserve.udp.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.RoleDto;
import org.technoserve.udp.dto.ValueChainResponse;
import org.technoserve.udp.entity.valuechain.ValueChain;
import org.technoserve.udp.service.RoleService;
import org.technoserve.udp.service.ValueChainService;

import java.util.List;

/**
 * Controller for managing roles, including creating roles with permissions and properties,
 * retrieving all roles, and deleting roles.
 */
@Slf4j
@RestController
@RequestMapping("/role-management")
@RequiredArgsConstructor
public class RoleManagementController {

    private final RoleService roleService;
    private final ValueChainService valueChainService;

    /**
     * Creates a role with assigned permissions and properties.
     *
     * @param roleDto Role data transfer object containing role details.
     * @return Success message if the role is created successfully.
     */
    @PostMapping("/assign-permissions-and-properties")
    public ResponseEntity<String> createRoleWithPermissionsAndProperties(@RequestBody RoleDto roleDto) {
        log.info("Received request to create role: {}", roleDto.getName());
        String response = roleService.saveOrUpdateRole(roleDto);
        log.info("Role '{}' created successfully", roleDto.getName());
        return ResponseEntity.ok(response);
    }

    /**
     * Retrieves all roles except the ADMIN role.
     *
     * @return List of RoleDto objects.
     */
    @GetMapping("/get-all-roles")
    public ResponseEntity<List<RoleDto>> getAllRoles() {
        log.info("Fetching all roles excluding ADMIN");
        List<RoleDto> roles = roleService.getAllRoles();
        log.info("Retrieved {} roles", roles.size());
        return ResponseEntity.ok(roles);
    }


    /**
     *
     * @return List of All value chain objects.
     */
    @GetMapping("/get-all-value-chain")
    public ResponseEntity<List<ValueChainResponse>> getAllValueChain() {
        List<ValueChainResponse> valueChains = valueChainService.listAllValueChains();
        return ResponseEntity.ok(valueChains);
    }

    /**
     * Deletes a role by its name.
     *
     * @param roleName Name of the role to be deleted.
     * @return Success message if deletion is successful.
     */
    @DeleteMapping("/delete-role/{roleName}")
    public ResponseEntity<String> deleteRole(@PathVariable String roleName) {
        log.info("Received request to delete role: {}", roleName);
        String response = roleService.deleteRoleByName(roleName);
        log.info("Role '{}' deleted successfully", roleName);
        return ResponseEntity.ok(response);
    }
    /**
     * Retrieves a paginated list of roles, excluding those with the ADMIN role.
     *
     * <p>This API supports filtering by roleName, sorting, and pagination.
     *
     * @param roleName (Optional) Filter users whose email contains this value (case-insensitive).
     * @param page (Default: 0) The page number to retrieve (zero-based index).
     * @param size (Default: 10) Number of records per page.
     * @param sortBy (Default: "email") The field to sort results by.
     * @param sortDir (Default: "asc") Sorting direction: "asc" for ascending, "desc" for descending.
     * @return ResponseEntity containing a paginated list of UserDto objects.
     */
    @GetMapping
    public ResponseEntity<Page<RoleDto>> getAllRoles(
            @RequestParam(required = false) String roleName,  // Filtering by role name
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {

        Sort sort = Sort.by(sortBy);
        sort = sortDir.equalsIgnoreCase("desc") ? sort.descending() : sort.ascending();

        Pageable pageable = PageRequest.of(page, size, sort);
        Page<RoleDto> roles = roleService.getAllRoles(roleName, pageable);
        return ResponseEntity.ok(roles);
    }
}
