package org.technoserve.udp.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.FundCodeRequest;
import org.technoserve.udp.service.FundCodeService;

@RestController
@RequestMapping("/fund-code")
@RequiredArgsConstructor
public class FundCodeController {

  private final FundCodeService fundCodeService;

  @PostMapping("/create")
  public ResponseEntity<ApiResponse> createFundCode(@Valid @RequestBody FundCodeRequest request) {
    ApiResponse response = fundCodeService.createFundCode(request);
    return new ResponseEntity<>(response, HttpStatus.CREATED);
  }

  @PutMapping("/update/{id}")
  public ResponseEntity<ApiResponse> updateFundCode(@PathVariable Long id, @Valid @RequestBody FundCodeRequest request) {
    ApiResponse response = fundCodeService.updateFundCode(id, request);
    return ResponseEntity.ok(response);
  }

  @DeleteMapping("/delete/{id}")
  public ResponseEntity<Void> deleteFundCode(@PathVariable Long id) {
    fundCodeService.deleteFundCode(id);
    return ResponseEntity.noContent().build();
  }


}
