package org.technoserve.udp.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.ProgramResponse;
import org.technoserve.udp.dto.ValueChainRequest;
import org.technoserve.udp.dto.ValueChainResponse;
import org.technoserve.udp.entity.valuechain.ValueChainType;
import org.technoserve.udp.service.ProgramService;
import org.technoserve.udp.service.ValueChainService;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/value-chain")
public class ValueChainController {

  private final ValueChainService valueChainService;
  private final ProgramService programService;

  /**
   * Creates a new ValueChain.
   */
  @PostMapping("/create")
  public ResponseEntity<ApiResponse> createValueChain(@Valid @RequestBody ValueChainRequest requestDTO) {
    ApiResponse response = valueChainService.createValueChain(requestDTO);
    return ResponseEntity.status(201).body(response);
  }

  /**
   * Updates an existing ValueChain.
   */
  @PutMapping("/update/{id}")
  public ResponseEntity<ApiResponse> updateValueChain(@PathVariable Long id,
                                                      @Valid @RequestBody ValueChainRequest requestDTO) {
    ApiResponse response = valueChainService.updateValueChain(id, requestDTO);
    return ResponseEntity.ok(response);
  }

  /**
   * Deletes a ValueChain.
   */
  @DeleteMapping("/delete/{id}")
  public ResponseEntity<ApiResponse> deleteValueChain(@PathVariable Long id) {
    ApiResponse response = valueChainService.deleteValueChain(id);
    return ResponseEntity.ok(response);
  }

  /**
   * Retrieves all ValueChains.
   */
  @GetMapping("/list")
  public ResponseEntity<List<ValueChainResponse>> listValueChains(@AuthenticationPrincipal OidcUser oidcUser) {
    List<ValueChainResponse> valueChains = valueChainService.listValueChainsAccessedByRole(oidcUser);
    return ResponseEntity.ok(valueChains);
  }

  /**
   * Retrieves all ValueChains.
   */
  @GetMapping("/{id}/program-list")
  public ResponseEntity<List<ProgramResponse>> listOfProgramsInValueChains(@PathVariable Long id) {
    List<ProgramResponse> valueChains = programService.listActiveProgramsByValueChain(id);
    return ResponseEntity.ok(valueChains);
  }

  /**
   * Retrieves all ValueChains.
   */
  @GetMapping("/{id}/approved-program-list")
  public ResponseEntity<List<ProgramResponse>> listOfApprovedProgramsInValueChains(@PathVariable Long id) {
    List<ProgramResponse> valueChains = programService.listOfApprovedProgramsInValueChains(id);
    return ResponseEntity.ok(valueChains);
  }
  @GetMapping("/types")
  public ResponseEntity<List<ValueChainType>> listValueChainTypes() {
    List<ValueChainType> valueChainTypes = valueChainService.listOfValueChainTypes();
    return ResponseEntity.ok(valueChainTypes);
  }
}
