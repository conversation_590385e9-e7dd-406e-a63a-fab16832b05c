package org.technoserve.udp.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.*;
import org.technoserve.udp.entity.program.ProgramPhase;
import org.technoserve.udp.service.*;

import java.util.List;

@RestController
@RequestMapping("/program")
@RequiredArgsConstructor
public class ProgramController {

  private final ProgramService programService;
  private final FundCodeService fundCodeService;
  private final ProgramFactSheetService programFactSheetService;
  private final ProgramLogFrameService programLogFrameSService;
  private final MicroActivityService microActivityService;
  private final TeamService teamService;

  /** Create new program as Draft**/
  @PostMapping("/create")
  public ResponseEntity<ApiResponse> createProgram(@Valid @RequestBody ProgramRequest programDTO) {
    return ResponseEntity.status(201).body(programService.createProgram(programDTO));
  }

  /** update program **/
  @PutMapping("/update/{id}")
  public ResponseEntity<ApiResponse> updateProgram(
      @PathVariable Long id,
      @Valid @RequestBody ProgramRequest programDTO) {
    return ResponseEntity.ok(programService.updateProgram(id, programDTO));
  }

  /** delete program **/
  @DeleteMapping("/delete/{id}")
  public ResponseEntity<ApiResponse> deleteProgram(@PathVariable Long id) {
    return ResponseEntity.ok(programService.softDeleteProgram(id));
  }

  @GetMapping("/{id}/fund-code-list")
  public ResponseEntity<FundCodesResponse> listOfFundCodeByProgram(@PathVariable Long id){
    return ResponseEntity.ok(fundCodeService.listOfFundCodesByProgram(id));
  }

  @GetMapping("/{id}/fact-sheet")
  public ResponseEntity<ProgramFactSheetResponse> factSheetByProgram(@PathVariable Long id){
    return ResponseEntity.ok(programFactSheetService.getProgramFactSheet(id));
  }

  @GetMapping("/{id}/log-frame")
  public ResponseEntity<ProgramLogFrameDto> logFrameByProgram(@PathVariable Long id){
    return ResponseEntity.ok(programLogFrameSService.getProgramLogFrameByProgramId(id));
  }

  @GetMapping("/{programId}/micro-activities/financial")
  public ResponseEntity<List<MicroActivityBudgetDTO>> getFinancialMicroActivities(@PathVariable Long programId) {
    return ResponseEntity.ok(microActivityService.getFinancialMicroActivities(programId));
  }

  @GetMapping("/{programId}/team")
  public ResponseEntity<List<TeamDTO>> getTeams(@PathVariable Long programId){
    return ResponseEntity.ok(teamService.getTeamsByProgram(programId));
  }

  @PutMapping("/{programId}/team")
  public ResponseEntity<ApiResponse> updateTeams(@PathVariable Long programId,@Valid @RequestBody List<TeamDTO> teams){
    return ResponseEntity.ok(teamService.updateTeamsForProgram(programId,teams));
  }

  @PutMapping("/{programId}/submit")
  public ResponseEntity<ApiResponse> submitForApproval(@PathVariable Long programId){
    return ResponseEntity.ok(programService.submitProgram(programId));
  }

  @PutMapping("/{programId}/review")
  public ResponseEntity<ApiResponse> reviewProgram(@PathVariable Long programId, @RequestParam("comment") String comment ,@AuthenticationPrincipal OidcUser oidcUser){
    return ResponseEntity.ok(programService.reviewProgram( programId, comment, oidcUser));
  }

  @PutMapping("/{programId}/approve")
  public ResponseEntity<ApiResponse> approveProgram(@PathVariable Long programId, @RequestParam("comment") String comment ,@AuthenticationPrincipal OidcUser oidcUser){
    return ResponseEntity.ok(programService.approveProgram( programId, comment, oidcUser));
  }

  @PutMapping("/{programId}/reject")
  public ResponseEntity<ApiResponse> rejectProgram(@PathVariable Long programId, @RequestParam("comment") String comment ,@AuthenticationPrincipal OidcUser oidcUser){
    return ResponseEntity.ok(programService.rejectProgram( programId, comment, oidcUser));
  }

  /**
   * Get all partners by program ID
   *
   * @param programId The program ID
   * @return List of partner responses
   */
  @GetMapping("/{programId}/partners")
  public ResponseEntity<List<PartnerResponse>> getPartnersByProgramId(@PathVariable Long programId) {
    return ResponseEntity.ok(programService.getPartnersByProgramId(programId));
  }

  @PutMapping("/{programId}/program-phase")
  public ResponseEntity<ApiResponse> updateProgramPhase(@PathVariable Long programId, @RequestParam("programPhase") ProgramPhase programPhase) {
    return ResponseEntity.ok(programService.updateProgramPhase(programId, programPhase));
  }

}
