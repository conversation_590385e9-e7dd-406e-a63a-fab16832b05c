package org.technoserve.udp.controller;

import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.SponsorRequest;
import org.technoserve.udp.dto.SponsorResponse;
import org.technoserve.udp.service.SponsorService;

import java.util.List;

@RestController
@RequestMapping("/sponsor")
public class SponsorController {

  private final SponsorService sponsorService;

  public SponsorController(SponsorService sponsorService) {
    this.sponsorService = sponsorService;
  }

  @GetMapping("/list")
  public ResponseEntity<List<SponsorResponse>> getAllSponsors() {
    return ResponseEntity.ok(sponsorService.getAllSponsors());
  }

  @PostMapping("/create")
  public ResponseEntity<ApiResponse> createSponsor(@Valid @RequestBody SponsorRequest sponsorDto) {
    return new ResponseEntity<>(sponsorService.createSponsor(sponsorDto), HttpStatus.CREATED);
  }

  @PutMapping("/update/{sponsorId}")
  public ResponseEntity<ApiResponse> updateSponsor(@PathVariable Long sponsorId,@Valid @RequestBody SponsorRequest sponsorDto) {
    return ResponseEntity.ok(sponsorService.updateSponsor(sponsorId, sponsorDto));
  }

  @DeleteMapping("/delete/{sponsorId}")
  public ResponseEntity<ApiResponse> deleteSponsor(@PathVariable Long sponsorId) {
    return ResponseEntity.ok(sponsorService.deleteSponsor(sponsorId));
  }
}