package org.technoserve.udp.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.annotation.RegisteredOAuth2AuthorizedClient;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.technoserve.udp.dto.TokenResponse;
import org.technoserve.udp.service.RoleService;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controller for handling OAuth2 authentication and token retrieval.
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class OAuth2TokenController {
  @Autowired
  private RoleService roleService;
  /**
   * Retrieves the OAuth2 access token along with issued and expiration timestamps.
   *
   * @param client OAuth2AuthorizedClient instance for Google authentication.
   * @return TokenResponse containing access token and timestamps.
   */
  @GetMapping("/token")
  public ResponseEntity<TokenResponse> getAccessToken(
          @RegisteredOAuth2AuthorizedClient("google") OAuth2AuthorizedClient client) {

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    OidcUser oidcUser = (OidcUser) authentication.getPrincipal();
    Instant issuedAt = client.getAccessToken().getIssuedAt();
    Instant expiresAt = client.getAccessToken().getExpiresAt();

    log.info("Access token retrieved for user: {}, Issued At: {}, Expires At: {}",
            oidcUser.getEmail(), issuedAt, expiresAt);

    return ResponseEntity.ok(new TokenResponse(oidcUser.getIdToken().getTokenValue(), issuedAt, expiresAt));
  }

  /**
   * Retrieves authenticated user details including email, name, profile picture, and roles.
   *
   * @return A ResponseEntity containing user details or an error message if not authenticated.
   */
  @GetMapping("/user-details")
  public ResponseEntity<Map<String, Object>> getUserDetails() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    Object principal = authentication.getPrincipal();

    if (principal instanceof OidcUser oidcUser) {
      // Extract roles
      List<String> roles = oidcUser.getAuthorities().stream()
              .map(grantedAuthority -> grantedAuthority.getAuthority().replace("ROLE_", "")) // Remove "ROLE_" prefix
              .collect(Collectors.toList());

      // Build user details map
      Map<String, Object> userDetails = Map.of(
              "email", oidcUser.getAttribute("email"),
              "name", oidcUser.getAttribute("name"),
              "picture", oidcUser.getAttribute("picture"),
              "given_name", oidcUser.getAttribute("given_name"),
              "family_name", oidcUser.getAttribute("family_name"),
              "roles", roles,
              "screens", roleService.getAuthScreens(roles)
      );

      log.info("User details fetched: {}", userDetails);
      return ResponseEntity.ok(userDetails);
    }

    log.warn("Unauthorized access attempt to user-details endpoint.");
    return ResponseEntity.status(401).body(Map.of("error", "User not authenticated"));
  }
}
