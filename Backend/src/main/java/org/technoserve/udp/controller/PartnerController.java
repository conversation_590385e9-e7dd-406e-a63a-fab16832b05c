package org.technoserve.udp.controller;

import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.PartnerRequest;
import org.technoserve.udp.dto.PartnerResponse;
import org.technoserve.udp.service.PartnerService;

import java.util.List;

@RestController
@RequestMapping("/partner")
public class PartnerController {

  private final PartnerService partnerService;

  public PartnerController(PartnerService partnerService) {
    this.partnerService = partnerService;
  }

  @GetMapping("/list")
  public ResponseEntity<List<PartnerResponse>> getAllPartners() {
    return ResponseEntity.ok(partnerService.getAllPartners());
  }

  @PostMapping("/create")
  public ResponseEntity<ApiResponse> createPartner(@Valid @RequestBody PartnerRequest partnerRequest) {
    return ResponseEntity.status(201).body(partnerService.createPartner(partnerRequest));
  }

  @PutMapping("/update/{partnerId}")
  public ResponseEntity<ApiResponse> updatePartner(@PathVariable Long partnerId, @Valid @RequestBody PartnerRequest partnerRequest) {
    return ResponseEntity.ok(partnerService.updatePartner(partnerId, partnerRequest));
  }

  @DeleteMapping("/delete/{partnerId}")
  public ResponseEntity<ApiResponse> deletePartner(@PathVariable Long partnerId) {
    return ResponseEntity.ok(partnerService.deletePartner(partnerId));
  }
}
