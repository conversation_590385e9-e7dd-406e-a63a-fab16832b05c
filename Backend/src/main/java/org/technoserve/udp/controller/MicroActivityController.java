package org.technoserve.udp.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.BudgetDTO;
import org.technoserve.udp.dto.EnumResponse;
import org.technoserve.udp.entity.program.BudgetFrequency;
import org.technoserve.udp.entity.program.MicroActivityFrequency;
import org.technoserve.udp.service.MicroActivityService;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/micro-activities")
public class MicroActivityController {

  private final MicroActivityService microActivityService;

  public MicroActivityController(MicroActivityService microActivityService) {
    this.microActivityService = microActivityService;
  }

  @PostMapping("/{microActivityId}/budget")
  public ResponseEntity<ApiResponse> createBudget(@PathVariable Long microActivityId, @RequestBody BudgetDTO budgetDTO) {
    return ResponseEntity.status(201).body(microActivityService.createBudget(microActivityId,budgetDTO));
  }

  @PutMapping("/{microActivityId}/budget")
  public ResponseEntity<ApiResponse> updateBudget(@PathVariable Long microActivityId,
                                                  @RequestBody BudgetDTO budgetDTO) {
    return ResponseEntity.ok(microActivityService.updateBudget(microActivityId, budgetDTO));
  }

  @GetMapping("/budget-frequencies")
  public List<EnumResponse> getFrequencies() {
    return Arrays.stream(BudgetFrequency.values())
        .map(frequency -> new EnumResponse(frequency.name(), frequency.getDisplayValue()))
        .toList();
  }

}