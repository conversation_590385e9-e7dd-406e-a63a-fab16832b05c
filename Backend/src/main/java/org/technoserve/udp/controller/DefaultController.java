package org.technoserve.udp.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.technoserve.udp.repository.UserRepository;

import java.io.IOException;

/**
 * DefaultController handles the default root (`/`) request.
 * It redirects users based on their existence in the system.
 */
@Slf4j // Enables logging
@RestController
public class DefaultController {

      /**
     * Redirects users based on their existence in the database.
     *
     * @param user                Authenticated user from OIDC.
     * @param httpServletRequest  Incoming HTTP request.
     * @param httpServletResponse HTTP response to handle redirection.
     * @throws IOException If an error occurs during redirection.
     */
    @GetMapping("/")
    public void redirectToHomepage(@AuthenticationPrincipal OidcUser user,
                                   HttpServletRequest httpServletRequest,
                                   HttpServletResponse httpServletResponse) throws IOException {
        log.info("Received request for redirect. Checking user existence...");

        // Extract the user's email from the OIDC user details
        String userEmail = user.getEmail();
        log.info("Authenticated user: {}", userEmail);


        // Extract base URL (before `/udp/`)
        String requestURL = httpServletRequest.getRequestURL().toString();
        String baseURL = requestURL.substring(0, requestURL.indexOf("/udp/"));
        log.info("Base URL extracted: {}", baseURL);

        log.info("Redirecting existing user {} to homepage: {}", userEmail, baseURL);
        httpServletResponse.sendRedirect(baseURL+"/auth");

    }
}
