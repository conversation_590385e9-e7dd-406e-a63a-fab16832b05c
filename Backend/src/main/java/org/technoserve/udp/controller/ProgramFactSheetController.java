package org.technoserve.udp.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.ProgramFactSheetRequest;
import org.technoserve.udp.dto.EnumResponse;
import org.technoserve.udp.entity.program.ProgramPhase;
import org.technoserve.udp.service.ProgramFactSheetService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/program-fact-sheet")
@RequiredArgsConstructor
public class ProgramFactSheetController {

  private final ProgramFactSheetService programFactSheetService;

  @PostMapping("/create")
  public ResponseEntity<ApiResponse> createProgramFactSheet(@Valid @RequestBody ProgramFactSheetRequest programFactSheetRequest) {
    ApiResponse response = programFactSheetService.createProgramFactSheet(programFactSheetRequest);
    return ResponseEntity.status(201).body(response);
  }

  @PutMapping("/update/{id}")
  public ResponseEntity<ApiResponse> updateProgramFactSheet(
      @PathVariable Long id,
      @Valid @RequestBody ProgramFactSheetRequest programFactSheetRequest) {
    ApiResponse response = programFactSheetService.updateProgramFactSheet(id, programFactSheetRequest);
    return ResponseEntity.ok(response);
  }

  @GetMapping("/program-phases")
  public List<EnumResponse> getProgramPhases() {
    return Arrays.stream(ProgramPhase.values())
        .map(phase -> new EnumResponse(phase.name(), phase.getDisplayValue()))
        .toList();
  }
}
