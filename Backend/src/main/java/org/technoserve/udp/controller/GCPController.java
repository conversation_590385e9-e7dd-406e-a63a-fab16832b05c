package org.technoserve.udp.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.technoserve.udp.service.GcpCloudServices;


@RestController
@RequestMapping("/gcp")
@RequiredArgsConstructor
public class GCPController {

  private final GcpCloudServices gcpCloudServices;

  @PostMapping("/upload-logo")
  public ResponseEntity<String> uploadLogo(
      @RequestParam("logo") MultipartFile logo) {
       return ResponseEntity.ok(gcpCloudServices.uploadLogo(logo));
  }
}
