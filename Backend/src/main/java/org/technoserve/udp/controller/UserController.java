package org.technoserve.udp.controller;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.UpdateUserRequest;
import org.technoserve.udp.dto.UserCreateRequest;
import org.technoserve.udp.dto.UserDto;
import org.technoserve.udp.entity.auth.UserStatus;
import org.technoserve.udp.service.UserService;

@Slf4j // Enables logging for debugging and monitoring
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor  // Automatically injects dependencies via constructor
public class UserController {

  private final UserService userService;

  /**
   * Retrieves a paginated list of users, excluding those with the ADMIN role.
   *
   * <p>This API supports filtering by email, sorting, and pagination.
   *
   * @param email (Optional) Filter users whose email contains this value (case-insensitive).
   * @param page (Default: 0) The page number to retrieve (zero-based index).
   * @param size (Default: 10) Number of records per page.
   * @param sortBy (Default: "email") The field to sort results by.
   * @param sortDir (Default: "asc") Sorting direction: "asc" for ascending, "desc" for descending.
   * @return ResponseEntity containing a paginated list of UserDto objects.
   */
  @GetMapping
  public ResponseEntity<Page<UserDto>> getUsers(
          @RequestParam(required = false) String email,
          @RequestParam(defaultValue = "0") int page,
          @RequestParam(defaultValue = "10") int size,
          @RequestParam(defaultValue = "email") String sortBy,
          @RequestParam(defaultValue = "asc") String sortDir) {

    // Create a Sort object dynamically
    Sort sort = Sort.by(sortBy);
    sort = sortDir.equalsIgnoreCase("desc") ? sort.descending() : sort.ascending();

    Pageable pageable = PageRequest.of(page, size, sort);
    Page<UserDto> users = userService.getAllUsers(email, pageable);
    return ResponseEntity.ok(users);
  }

  /**
   * Creates a new user with the specified details and roles.
   *
   * @param userCreateRequest Request payload containing user details.
   * @return ResponseEntity containing a success message.
   */
  @PostMapping("/create")
  public ResponseEntity<String> createUser(@RequestBody UserCreateRequest userCreateRequest) {
    log.info("Creating a new user with email: {}", userCreateRequest.getEmail());
    String userSuccess = userService.createUser(userCreateRequest);
    log.info("User created successfully: {}", userCreateRequest.getEmail());
    return ResponseEntity.ok(userSuccess);
  }

  /**
   * Updates user details including name and roles.
   *
   * @param email User's email to identify the record.
   * @param updateUserRequest Request payload containing updated user details.
   * @return ResponseEntity containing a success message.
   */
  @PutMapping("/update/{email}")
  public ResponseEntity<String> updateUser(@PathVariable String email, @RequestBody(required = false) UpdateUserRequest updateUserRequest) {
    log.info("Updating user with email: {}", email);
    String updateStatus = userService.updateUser(email, updateUserRequest);
    log.info("User updated successfully: {}", email);
    return ResponseEntity.ok(updateStatus);
  }

  /**
   * Changes the status of a user (e.g., ACTIVE, INACTIVE).
   *
   * @param email User's email to identify the record.
   * @param status New status to be assigned.
   * @return ResponseEntity containing the updated UserEntity object.
   */
  @PatchMapping("/change-status/{email}")
  public ResponseEntity<String> changeUserStatus(@PathVariable String email, @RequestParam String status) {

    UserStatus userStatus;
    try {
      userStatus = UserStatus.valueOf(status.toUpperCase()); // Ensure case-insensitive match
    } catch (IllegalArgumentException e) {
      return ResponseEntity.badRequest().body("Invalid userStatus value: " + status);
    }
    log.info("Changing status for user {} to {}", email, userStatus);
    String success = userService.changeStatus(email, userStatus); // Must return UserEntity
    log.info("User status updated successfully: {} -> {}", email, userStatus);
    return ResponseEntity.ok(success);
  }

}
