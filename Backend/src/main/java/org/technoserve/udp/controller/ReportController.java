package org.technoserve.udp.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.service.ExcelExportService;
import org.technoserve.udp.service.MasterReportService;
import org.technoserve.udp.service.TransactionalReportService;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/report")
@RequiredArgsConstructor
public class ReportController {

  private final TransactionalReportService reportService;

  private final MasterReportService masterReportService;

  private final ExcelExportService excelExportService;

  /**
   * Get dairy output report with sorting and pagination
   *
   * @param partnerId The partner ID to filter by (optional)
   * @param programId The program ID to filter by (optional)
   * @param startDate The start date to filter by (optional, format: yyyy-MM-dd)
   * @param endDate The end date to filter by (optional, format: yyyy-MM-dd)
   * @param sortBy The field to sort by (default: partnerName)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @param page The page number (0-based, default: 0)
   * @param size The page size (default: 10)
   * @return Map containing paginated and sorted dairy output report DTOs and metadata
   */
  @GetMapping("/dairy-output")
  public ResponseEntity<Map<String, Object>> getDairyOutputReport(
          @RequestParam(required = false) Long partnerId,
          @RequestParam(required = false) Long programId,
          @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
          @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
          @RequestParam(defaultValue = "partnerName") String sortBy,
          @RequestParam(defaultValue = "asc") String sortDir,
          @RequestParam(defaultValue = "0") int page,
          @RequestParam(defaultValue = "10") int size) {

    Map<String, Object> response = reportService.generateDairyOutputReport(
            partnerId, programId, startDate, endDate, sortBy, sortDir, page, size);
    return ResponseEntity.ok(response);
  }

  @GetMapping("/cotton-output")
  public ResponseEntity<Map<String, Object>> getCottonFarmingReport(
          @RequestParam(required = false) Long partnerId,
          @RequestParam(required = false) Long programId,
          @RequestParam (required = true) List<Integer> years,
          @RequestParam(defaultValue = "farmerName") String sortBy,
          @RequestParam(defaultValue = "asc") String sortDir,
          @RequestParam(defaultValue = "0") int page,
          @RequestParam(defaultValue = "10") int size) {

    Map<String, Object> response = reportService.generateCottonFarmingReport(
            partnerId, programId, years, sortBy, sortDir, page, size);
    return ResponseEntity.ok(response);
  }

  /**
   * Get farmer report with sorting and pagination
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by (default: farmerName)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @param page The page number (0-based, default: 0)
   * @param size The page size (default: 10)
   * @return Map containing paginated and sorted farmer report DTOs and metadata
   */
  @GetMapping("/farmers")
  public ResponseEntity<Map<String, Object>> getFarmerReport(
          @RequestParam(required = false) Long programId,
          @RequestParam(required = false) Long partnerId,
          @RequestParam(defaultValue = "farmerName") String sortBy,
          @RequestParam(defaultValue = "asc") String sortDir,
          @RequestParam(defaultValue = "0") int page,
          @RequestParam(defaultValue = "10") int size) {

    Map<String, Object> response = masterReportService.generateFarmerReport(
            programId, partnerId, sortBy, sortDir, page, size);
    return ResponseEntity.ok(response);
  }

  /**
   * Get centre report with sorting and pagination
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by (default: centreName)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @param page The page number (0-based, default: 0)
   * @param size The page size (default: 10)
   * @return Map containing paginated and sorted centre report DTOs and metadata
   */
  @GetMapping("/centres")
  public ResponseEntity<Map<String, Object>> getCentreReport(
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) Long partnerId,
      @RequestParam(defaultValue = "centreName") String sortBy,
      @RequestParam(defaultValue = "asc") String sortDir,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size) {

    Map<String, Object> response = masterReportService.generateCentreReport(
        programId, partnerId, sortBy, sortDir, page, size);
    return ResponseEntity.ok(response);
  }

  /**
   * Get staff report with sorting and pagination
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by (default: name)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @param page The page number (0-based, default: 0)
   * @param size The page size (default: 10)
   * @return Map containing paginated and sorted staff report DTOs and metadata
   */
  @GetMapping("/staffs")
  public ResponseEntity<Map<String, Object>> getStaffReport(
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) Long partnerId,
      @RequestParam(defaultValue = "name") String sortBy,
      @RequestParam(defaultValue = "asc") String sortDir,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size) {

    Map<String, Object> response = masterReportService.generateStaffReport(
        programId, partnerId, sortBy, sortDir, page, size);
    return ResponseEntity.ok(response);
  }

  /**
   * Get distinct years from cotton farming data filtered by program ID and partner ID
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @return List of distinct years in ascending order
   */
  @GetMapping("/cotton-years")
  public ResponseEntity<List<Integer>> getDistinctCottonFarmingYears(
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) Long partnerId) {

    List<Integer> years = reportService.getDistinctCottonFarmingYears(programId, partnerId);
    return ResponseEntity.ok(years);
  }


  /**
   * Download centre report as Excel file
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by (default: centreName)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @return Excel file as byte array for download
   */
  @GetMapping("/centres/download")
  public ResponseEntity<byte[]> downloadCentreReportExcel(
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) Long partnerId,
      @RequestParam(defaultValue = "centreName") String sortBy,
      @RequestParam(defaultValue = "asc") String sortDir,
      @RequestParam List<Integer> headerIndexes) {

    try {
      byte[] excelData = excelExportService.generateCentreReportExcel(
          programId, partnerId, sortBy, sortDir, headerIndexes);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setContentDispositionFormData("attachment", "centre_report.xlsx");
      headers.setContentLength(excelData.length);

      return ResponseEntity.ok()
          .headers(headers)
          .body(excelData);
    } catch (Exception e) {
      return ResponseEntity.internalServerError().build();
    }
  }

  /**
   * Download farmer report as Excel file
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by (default: farmerName)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @return Excel file as byte array for download
   */
  @GetMapping("/farmers/download")
  public ResponseEntity<byte[]> downloadFarmerReportExcel(
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) Long partnerId,
      @RequestParam(defaultValue = "farmerName") String sortBy,
      @RequestParam(defaultValue = "asc") String sortDir) {

    try {
      byte[] excelData = excelExportService.generateFarmerReportExcel(
          programId, partnerId, sortBy, sortDir);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setContentDispositionFormData("attachment", "farmer_report.xlsx");
      headers.setContentLength(excelData.length);

      return ResponseEntity.ok()
          .headers(headers)
          .body(excelData);
    } catch (Exception e) {
      return ResponseEntity.internalServerError().build();
    }
  }

  /**
   * Download staff report as Excel file
   *
   * @param programId The program ID to filter by (optional)
   * @param partnerId The partner ID to filter by (optional)
   * @param sortBy The field to sort by (default: name)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @return Excel file as byte array for download
   */
  @GetMapping("/staffs/download")
  public ResponseEntity<byte[]> downloadStaffReportExcel(
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) Long partnerId,
      @RequestParam(defaultValue = "name") String sortBy,
      @RequestParam(defaultValue = "asc") String sortDir) {

    try {
      byte[] excelData = excelExportService.generateStaffReportExcel(
          programId, partnerId, sortBy, sortDir);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setContentDispositionFormData("attachment", "staff_report.xlsx");
      headers.setContentLength(excelData.length);

      return ResponseEntity.ok()
          .headers(headers)
          .body(excelData);
    } catch (Exception e) {
      return ResponseEntity.internalServerError().build();
    }
  }

  /**
   * Download dairy output report as Excel file
   *
   * @param partnerId The partner ID to filter by (optional)
   * @param programId The program ID to filter by (optional)
   * @param startDate The start date to filter by (optional, format: yyyy-MM-dd)
   * @param endDate The end date to filter by (optional, format: yyyy-MM-dd)
   * @param sortBy The field to sort by (default: partnerName)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @return Excel file as byte array for download
   */
  @GetMapping("/dairy-output/download")
  public ResponseEntity<byte[]> downloadDairyOutputReportExcel(
      @RequestParam(required = false) Long partnerId,
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
      @RequestParam(defaultValue = "partnerName") String sortBy,
      @RequestParam(defaultValue = "asc") String sortDir) {

    try {
      byte[] excelData = excelExportService.generateDairyOutputReportExcel(
          partnerId, programId, startDate, endDate, sortBy, sortDir);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setContentDispositionFormData("attachment", "dairy_output_report.xlsx");
      headers.setContentLength(excelData.length);

      return ResponseEntity.ok()
          .headers(headers)
          .body(excelData);
    } catch (Exception e) {
      return ResponseEntity.internalServerError().build();
    }
  }

  @GetMapping("/dairy-field-data/download")
  public ResponseEntity<byte[]> downloadDairyFieldDataReportExcel(
      @RequestParam(required = false) Long partnerId,
      @RequestParam(required = false) Long programId,
      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
      @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

    try {
      byte[] excelData = excelExportService.generateDairyFieldDataReportExcel(
          partnerId, programId, startDate, endDate);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setContentDispositionFormData("attachment", "dairy_field_data_report.xlsx");
      headers.setContentLength(excelData.length);

      return ResponseEntity.ok()
          .headers(headers)
          .body(excelData);
    } catch (Exception e) {
      return ResponseEntity.internalServerError().build();
    }
  }


  /**
   * Download cotton farming report as Excel file
   *
   * @param partnerId The partner ID to filter by (optional)
   * @param programId The program ID to filter by (optional)
   * @param years The list of years to filter by (required)
   * @param sortBy The field to sort by (default: farmerName)
   * @param sortDir The sort direction (asc or desc, default: asc)
   * @return Excel file as byte array for download
   */
  @GetMapping("/cotton-output/download")
  public ResponseEntity<byte[]> downloadCottonFarmingReportExcel(
      @RequestParam(required = false) Long partnerId,
      @RequestParam(required = false) Long programId,
      @RequestParam(required = true) List<Integer> years,
      @RequestParam(defaultValue = "farmerName") String sortBy,
      @RequestParam(defaultValue = "asc") String sortDir) {

    try {
      byte[] excelData = excelExportService.generateCottonFarmingReportExcel(
          partnerId, programId, years, sortBy, sortDir);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
      headers.setContentDispositionFormData("attachment", "cotton_farming_report.xlsx");
      headers.setContentLength(excelData.length);

      return ResponseEntity.ok()
          .headers(headers)
          .body(excelData);
    } catch (Exception e) {
      return ResponseEntity.internalServerError().build();
    }
  }

}
