package org.technoserve.udp.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.EnumResponse;
import org.technoserve.udp.dto.ProgramLogFrameRequest;
import org.technoserve.udp.entity.program.MicroActivityFrequency;
import org.technoserve.udp.entity.program.MicroActivityType;
import org.technoserve.udp.service.ProgramLogFrameService;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/program-log-frame")
@RequiredArgsConstructor
public class ProgramLogFrameController {

  private final ProgramLogFrameService programLogFrameService;

  // Create a new full log frame hierarchy.
  @PostMapping("/create")
  public ResponseEntity<ApiResponse> createProgramLogFrame(@Valid @RequestBody ProgramLogFrameRequest request) {
    ApiResponse apiResponse = programLogFrameService.createFullLogFrame(request);
    return ResponseEntity.ok(apiResponse);
  }

  // Update an existing full log frame hierarchy using the log frame id as a path variable.
  @PutMapping("/update/{programLogFrameId}")
  public ResponseEntity<ApiResponse> updateProgramLogFrame(
      @PathVariable Long programLogFrameId,
      @RequestBody ProgramLogFrameRequest request) {
    ApiResponse apiResponse = programLogFrameService.updateFullLogFrame(programLogFrameId, request);
    return ResponseEntity.ok(apiResponse);
  }

  @GetMapping("/micro-activity-types")
  public List<EnumResponse> getMicroActivityTypes() {
    return Arrays.stream(MicroActivityType.values())
        .map(type -> new EnumResponse(type.name(), type.getDisplayValue()))
        .toList();
  }

  @GetMapping("/micro-activity-frequencies")
  public List<EnumResponse> getFrequencies() {
    return Arrays.stream(MicroActivityFrequency.values())
        .map(frequency -> new EnumResponse(frequency.name(), frequency.getDisplayValue()))
        .toList();
  }

}
