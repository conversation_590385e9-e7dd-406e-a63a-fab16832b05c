package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.*;
import org.technoserve.udp.repository.CentreRepository;
import org.technoserve.udp.repository.PartnerRepository;
import org.technoserve.udp.repository.ProgramRepository;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ExcelExportService {

    private final CentreRepository centreRepository;
    private final PartnerRepository partnerRepository;
    private final ProgramRepository programRepository;

    private final MasterReportService masterReportService;
    private final TransactionalReportService transactionalReportService;

    /**
     * Generate centre report as Excel file for download
     *
     * @param programId The program ID to filter by (optional)
     * @param partnerId The partner ID to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @param headerIndexes List of column indexes to include in the Excel export (optional)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateCentreReportExcel(Long programId, Long partnerId, String sortBy, String sortDir, List<Integer> headerIndexes) throws IOException {
        // Get all centre data without pagination for Excel export
        List<CentreReportDto> centreData = (List<CentreReportDto>) masterReportService.generateCentreReport(programId, partnerId, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Centre Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Define all available headers
            String[] allHeaders = {
                "Centre ID", "Centre Name", "Centre Type", "Route No", "Route Name", "Facilitator ID",
                "Facilitator Name", "Facilitator Country Code", "Facilitator Mobile Number",
                "License/Certification Type", "License/Certification Status", "License/Certification No",
                "State", "District", "Taluk", "Village", "Latitude", "Longitude",
                "Installed Capacity", "ICS Type", "Partner Name", "Program Name"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (CentreReportDto centre : centreData) {
                Row row = sheet.createRow(rowNum++);
                int colIndex = 0;
                setCellValue(row, colIndex++, centre.getCentreId(), dataStyle);
                setCellValue(row, colIndex++, centre.getCentreName(), dataStyle);
                setCellValue(row, colIndex++, centre.getCentreType(), dataStyle);
                setCellValue(row, colIndex++, centre.getRouteNo(), dataStyle);
                setCellValue(row, colIndex++, centre.getRouteName(), dataStyle);
                setCellValue(row, colIndex++, centre.getFacilitatorId(), dataStyle);
                setCellValue(row, colIndex++, centre.getFacilitatorName(), dataStyle);
                setCellValue(row, colIndex++, centre.getFacilitatorCountryCode(), dataStyle);
                setCellValue(row, colIndex++, centre.getFacilitatorMobileNumber(), dataStyle);
                setCellValue(row, colIndex++, centre.getLicenseCertificationType(), dataStyle);
                setCellValue(row, colIndex++, centre.getLicenseCertificationStatus(), dataStyle);
                setCellValue(row, colIndex++, centre.getLicenseCertificationNo(), dataStyle);
                setCellValue(row, colIndex++, centre.getState(), dataStyle);
                setCellValue(row, colIndex++, centre.getDistrict(), dataStyle);
                setCellValue(row, colIndex++, centre.getTaluk(), dataStyle);
                setCellValue(row, colIndex++, centre.getVillage(), dataStyle);
                setCellValue(row, colIndex++, centre.getLatitude(), dataStyle);
                setCellValue(row, colIndex++, centre.getLongitude(), dataStyle);
                setCellValue(row, colIndex++, centre.getInstalledCapacity(), dataStyle);
                setCellValue(row, colIndex++, centre.getIcsType(), dataStyle);
                setCellValue(row, colIndex++, centre.getPartnerName(), dataStyle);
                setCellValue(row, colIndex++, centre.getProgramName(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Generate farmer report as Excel file for download
     *
     * @param programId The program ID to filter by (optional)
     * @param partnerId The partner ID to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateFarmerReportExcel(Long programId, Long partnerId, String sortBy, String sortDir) throws IOException {
        // Get all farmer data without pagination for Excel export
        List<FarmerReportDto> farmerData = (List<FarmerReportDto>) masterReportService.generateFarmerReport(programId, partnerId, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Farmer Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Farmer ID", "Farmer Tracenet Code", "Farmer Name", "Age", "Gender", "Highest Education",
                "State", "District", "Village", "Country Code", "Mobile Number",
                "Centre ID", "Centre Name", "Centre Type", "Marital Status", "Spouse Name",
                "Caste", "House Hold Size", "Land Size Under Cultivation", "Land Measure Type",
                "Organic Status", "Herd Size", "Any Other Income Generating Activity", "Household Annual Income", "Agricultural Annual Income",
                "Dairy Annual Income", "Other Annual Income", "Crops Grown", "Cattle Breed Types", "Loan Amount",
                "Agricultural Loan", "Dairy Loan", "Partner Name", "Program Name", "Lat Long"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (FarmerReportDto farmer : farmerData) {
                Row row = sheet.createRow(rowNum++);
                int colIndex = 0;
                setCellValue(row, colIndex++, farmer.getFarmerId(), dataStyle);
                setCellValue(row, colIndex++, farmer.getFarmerTracenetCode(), dataStyle);
                setCellValue(row, colIndex++, farmer.getFarmerName(), dataStyle);
                setCellValue(row, colIndex++, farmer.getAge(), dataStyle);
                setCellValue(row, colIndex++, farmer.getGender(), dataStyle);
                setCellValue(row, colIndex++, farmer.getHighestEducation(), dataStyle);
                setCellValue(row, colIndex++, farmer.getState(), dataStyle);
                setCellValue(row, colIndex++, farmer.getDistrict(), dataStyle);
                setCellValue(row, colIndex++, farmer.getVillage(), dataStyle);
                setCellValue(row, colIndex++, farmer.getCountryCode(), dataStyle);
                setCellValue(row, colIndex++, farmer.getMobileNumber(), dataStyle);
                setCellValue(row, colIndex++, farmer.getCentreId(), dataStyle);
                setCellValue(row, colIndex++, farmer.getCentreName(), dataStyle);
                setCellValue(row, colIndex++, farmer.getCentreType(), dataStyle);
                setCellValue(row, colIndex++, farmer.getMaritalStatus(), dataStyle);
                setCellValue(row, colIndex++, farmer.getSpouseName(), dataStyle);
                setCellValue(row, colIndex++, farmer.getCaste(), dataStyle);
                setCellValue(row, colIndex++, farmer.getHouseHoldSize(), dataStyle);
                setCellValue(row, colIndex++, farmer.getLandSizeUnderCultivation(), dataStyle);
                setCellValue(row, colIndex++, farmer.getLandMeasureType(), dataStyle);
                setCellValue(row, colIndex++, farmer.getOrganicStatus(), dataStyle);
                setCellValue(row, colIndex++, farmer.getHerdSize(), dataStyle);
                setCellValue(row, colIndex++, farmer.getAnyOtherIncomeGeneratingActivity(), dataStyle);
                setCellValue(row, colIndex++, farmer.getHouseholdAnnualIncome(), dataStyle);
                setCellValue(row, colIndex++, farmer.getAgriculturalAnnualIncome(), dataStyle);
                setCellValue(row, colIndex++, farmer.getDairyAnnualIncome(), dataStyle);
                setCellValue(row, colIndex++, farmer.getOtherAnnualIncome(), dataStyle);
                setCellValue(row, colIndex++, farmer.getCropsGrown(), dataStyle);
                setCellValue(row, colIndex++, farmer.getCattleBreedTypes(), dataStyle);
                setCellValue(row, colIndex++, farmer.getLoanAmount(), dataStyle);
                setCellValue(row, colIndex++, farmer.getAgriculturalLoan(), dataStyle);
                setCellValue(row, colIndex++, farmer.getDairyLoan(), dataStyle);
                setCellValue(row, colIndex++, farmer.getPartnerName(), dataStyle);
                setCellValue(row, colIndex++, farmer.getProgramName(), dataStyle);
                setCellValue(row, colIndex, farmer.getLatLong(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Generate staff report as Excel file for download
     *
     * @param programId The program ID to filter by (optional)
     * @param partnerId The partner ID to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateStaffReportExcel(Long programId, Long partnerId, String sortBy, String sortDir) throws IOException {
        // Get all staff data without pagination for Excel export
        List<StaffReportDto> staffData = (List<StaffReportDto>) masterReportService.generateStaffReport(programId, partnerId, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Staff Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Staff ID", "Name", "Designation", "Gender", "Country Code",
                "Mobile Number", "Centre ID", "Centre Name", "Centre Type", "District",
                "State", "Partner Name", "Program Name"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (StaffReportDto staff : staffData) {
                Row row = sheet.createRow(rowNum++);
                int colIndex = 0;
                setCellValue(row, colIndex++, staff.getStaffId(), dataStyle);
                setCellValue(row, colIndex++, staff.getName(), dataStyle);
                setCellValue(row, colIndex++, staff.getDesignation(), dataStyle);
                setCellValue(row, colIndex++, staff.getGender(), dataStyle);
                setCellValue(row, colIndex++, staff.getCountryCode(), dataStyle);
                setCellValue(row, colIndex++, staff.getMobileNumber(), dataStyle);
                setCellValue(row, colIndex++, staff.getCentreId(), dataStyle);
                setCellValue(row, colIndex++, staff.getCentreName(), dataStyle);
                setCellValue(row, colIndex++, staff.getCentreType(), dataStyle);
                setCellValue(row, colIndex++, staff.getDistrict(), dataStyle);
                setCellValue(row, colIndex++, staff.getState(), dataStyle);
                setCellValue(row, colIndex++, staff.getPartnerName(), dataStyle);
                setCellValue(row, colIndex, staff.getProgramName(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Generate dairy output report as Excel file for download
     *
     * @param partnerId The partner ID to filter by (optional)
     * @param programId The program ID to filter by (optional)
     * @param startDate The start date to filter by (optional)
     * @param endDate The end date to filter by (optional)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateDairyOutputReportExcel(Long partnerId, Long programId, LocalDate startDate, LocalDate endDate, String sortBy, String sortDir) throws IOException {
        // Get all dairy output data without pagination for Excel export
        List<DairyOutputReportDto> dairyData = (List<DairyOutputReportDto>) transactionalReportService.generateDairyOutputReport(partnerId, programId, startDate, endDate, sortBy, sortDir, 0, Integer.MAX_VALUE).get("dairyOutputReport");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Dairy Output Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Partner Name", "Centre Type", "Centre Name", "Centre ID", "Installed Capacity",
                "Total Milk Volume", "Milk Received LPD", "Utilization Percentage", "Average Fat", "Average SNF",
                "Segregated Milk Volume", "AB Positive Milk Volume", "AFM1 Positive Milk Volume", "High Sodium Milk Volume", "Overall Positive Milk Volume",
                "Overall Negative Milk Volume", "QBI Volume Milk Volume", "Compliant Percentage", "AB Pos Percentage", "AFM1 Pos Percentage",
                "Beta Pos Volume", "Beta Pos Percentage", "Sulfa Pos Volume", "Sulfa Pos Percentage", "Tetra Pos Volume",
                "Tetra Pos Percentage", "Cap Pos Volume", "Cap Pos Percentage", "Afla Pos Volume", "Afla Pos Percentage"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (DairyOutputReportDto dairy : dairyData) {
                Row row = sheet.createRow(rowNum++);
                int colIndex = 0;
                setCellValue(row, colIndex++, dairy.getPartnerName(), dataStyle);
                setCellValue(row, colIndex++, dairy.getCentreType(), dataStyle);
                setCellValue(row, colIndex++, dairy.getCentreName(), dataStyle);
                setCellValue(row, colIndex++, dairy.getCentreId(), dataStyle);
                setCellValue(row, colIndex++, dairy.getInstalledCapacity(), dataStyle);
                setCellValue(row, colIndex++, dairy.getTotalMilkVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getMilkReceivedLPD(), dataStyle);
                setCellValue(row, colIndex++, dairy.getUtilizationPercentage(), dataStyle);
                setCellValue(row, colIndex++, dairy.getAverageFat(), dataStyle);
                setCellValue(row, colIndex++, dairy.getAverageSNF(), dataStyle);
                setCellValue(row, colIndex++, dairy.getSegregatedMilkVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getAbPositiveMilkVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getAfm1PositiveMilkVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getHighSodiumMilkVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getOverallPositiveMilkVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getOverallNegativeMilkVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getQbiVolumeMilkVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getCompliantPercentage(), dataStyle);
                setCellValue(row, colIndex++, dairy.getAbPosPercentage(), dataStyle);
                setCellValue(row, colIndex++, dairy.getAfm1PosPercentage(), dataStyle);
                setCellValue(row, colIndex++, dairy.getBetaPosVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getBetaPosPercentage(), dataStyle);
                setCellValue(row, colIndex++, dairy.getSulfaPosVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getSulfaPosPercentage(), dataStyle);
                setCellValue(row, colIndex++, dairy.getTetraPosVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getTetraPosPercentage(), dataStyle);
                setCellValue(row, colIndex++, dairy.getCapPosVolume(), dataStyle);
                setCellValue(row, colIndex++, dairy.getCapPosPercentage(), dataStyle);
                setCellValue(row, colIndex++, dairy.getAflaPosVolume(), dataStyle);
                setCellValue(row, colIndex, dairy.getAflaPosPercentage(), dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    public byte[] generateDairyFieldDataReportExcel(Long partnerId, Long programId, LocalDate startDate, LocalDate endDate) throws IOException {
        // Get all dairy field data without pagination for Excel export
        List<DairyFieldDataDto> dairyFieldData =  transactionalReportService.getLatestDairyFieldData(partnerId, programId, startDate, endDate);
        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Dairy Field Data Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Program Name", "Partner Name",
                "Total Farmers", "No. of Animal Welfare Farms", "No. of Women Empowerment", "Women Empowerment Percentage"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (DairyFieldDataDto dairy : dairyFieldData) {
                Row row = sheet.createRow(rowNum++);
                int colIndex = 0;

                setCellValue(row, colIndex++, dairy.getProgramName(), dataStyle);
                setCellValue(row, colIndex++, dairy.getPartnerName(), dataStyle);
                setCellValue(row, colIndex++, dairy.getTotalFarmers(), dataStyle);
                setCellValue(row, colIndex++, dairy.getNoOfAnimalWelfareFarms(), dataStyle);
                setCellValue(row, colIndex++, dairy.getNoOfWomenEmpowerment(), dataStyle);
                setCellValue(row, colIndex++, dairy.getWomenEmpowermentPercentage(), dataStyle);

            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }


    /**
     * Generate cotton farming report as Excel file for download
     * This creates a flattened Excel with farmer data and all year data in separate columns
     *
     * @param partnerId The partner ID to filter by (optional)
     * @param programId The program ID to filter by (optional)
     * @param years The list of years to filter by (required)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateCottonFarmingReportExcel(Long partnerId, Long programId, List<Integer> years, String sortBy, String sortDir) throws IOException {
        // Get all cotton farming data without pagination for Excel export
        List<CottonFarmingReportDto> cottonData = (List<CottonFarmingReportDto>) transactionalReportService.generateCottonFarmingReport(partnerId, programId, years, sortBy, sortDir, 0, Integer.MAX_VALUE).get("cottonFarmingReport");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Cotton Farming Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row with combined farmer and cotton farming data columns
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                // Farmer basic information
                "Farmer Id", "Farmer Tracenet Code", "Farmer Name", "Age", "Gender",
                "State", "District", "Village", "Country Code", "Mobile Number", "Center ID", "Centre Name", "Centre Type",
                "Marital Status", "Spouse Name", "Caste", "Highest education", "House Hold Size",
                "Land Size Under Cultivation", "Land Measure Type", "Organic Status", "Herd Size",
                "Any Other Income Generating Activity", "Household Annual Income", "Agricultural Annual Income",
                "Dairy Annual Income", "Other Annual Income", "Crops Grown", "Cattle Breed Types",
                "Loan Amount", "Agricultural Loan", "Dairy Loan", "Latitude and Longitudes", "Partner Name", "Program Name",

                // Year-specific cotton farming data
                "Year", "No. of males (adult) in household", "No. of females (adult) in household", "Children (<16) in household", "No. of school-going children", "No. of earning members in the family",
                "Total Landholding (in acres)", "Primary crop", "Secondary crops", "Non-organic Cotton land (in acre) (if any)", "Organic Cotton land (in acre)",
                "Years since practicing organic cotton (#)", "Certification status (certified/IC1..)", "Source of irrigation", "No. of cattle (cow and Buffalo)", "Source of drinking water",
                "Preferred selling point (Aggregator/Suminter/APMC/other Gin)", "Has space for harvested cotton storage (Yes/No)", "Receives any agro advisory (Yes/No)", "Received any training on best practices for organic cotton?",
                "Membership in FPO/FPC/SHG", "Maintaining any Diary or Register for record keeping (Yes/No)", "Annual household income(in Rs)", "Primary source of income", "Income from Primary source (Rs.)",
                "Certification cost per annum/acre", "Avg. production of organic cotton/acre (Kg)", "Cost of cultivation/acre (Rs)", "Quantity sold of organic cotton (in kg)",
                "Selling price per kg (Rs.)", "Material cost for bio-inputs", "Name of bio-input used for pest and disease management", "Name of bio-fertilizer/compost used", "No. of pheromone traps used / acre",
                "Cost per pheromone trap", "No. of Yellow sticky traps used / acre", "Cost per yellow sticky trap", "No. of Blue sticky traps used / acre", "Cost per blue sticky trap",
                "No. of bird perches used / acre", "Irrigation cost/acre", "No. of irrigation required for organic cotton", "Irrigation method used", "Any farm machinery hired (Yes/No)",
                "Cost of machinery hiring (Rs.)/acre", "Local labour cost per day", "Migrant labour cost per day", "No. of workers required during sowing/acre", "No. of workers required during harvesting/acre",
                "Harvesting time (1st, 2nd & 3rd picking) (month)", "Weeding method used (manual/mechanical)", "Weeding cost/acre", "Cost of mulching/acre", "No. of tillage practiced",
                "Tillage cost/acre", "Land preparation cost/acre", "Seed rate of organic cotton/acre", "Variety of organic cotton seed (Name)",
                "Name of border crop used", "Name of the inter crop used", "Name of cover crop", "Name of trap crop", "Mulching used (Yes/No)", "Type of mulching used (Bio-plastic/green/dry)", "What precautions used during storage",
                "Hired vehicle used for transportation of seed cotton (Yes/No)", "Transportation cost (Rs.)/Kg of seed cotton", "Any quantity rejection due to contamination/impurities (Kg)", "Price discovery mechanism",
                "Payment Transaction type (Cash/online)", "Days of credit after sell", "Availing any govt. scheme or subsidy benefits (Yes/No)", "Opted for crop insurance (Yes/No)", "Cost of crop insurance per acre",
                "Possess KCC (Yes/No)", "Possess active bank account (Yes/No)", "Crop rotation used (Yes/No)", "Crops used for rotation", "Using any water tracking devices (Yes/No)",
                "Capacity of pump (in HP)", "Maintaining Buffer zone (Yes/No)", "Utilization of crop residue (Fuel/cattle feed/biochar/in-situ composting/burning)",
                "Mode of payment to workers (cash/online)", "Any wage difference for Men and Women workers (Yes/No)", "Using any labour register (Yes/No)", "Any arrangement of safety-kit / first-aid for workers",
                "Any provision of shelter & safe drinking water for workers", "Any provision for lavatory for workers", "Involve family members (Women) in agricultural operations", "Any community water harvesting structure (Yes/No)", "Use of soil moisture meter (Yes/No)",

                // Calculated fields
                "Total HH Members", "Dependency Ratio", "Gender Ratio", "School Attendance Rate", "Total Cotton Land",
                "Organic Percent", "Land Used For Cotton", "Income Per Earner", "OC Income", "Profit Per Acre",
                "Total Certification Cost", "Total PT Cost", "Total YST Cost", "Total BST Cost", "Total Pest Mgmt Cost",
                "Total Labour Cost", "Machinery Cost Total", "Total Irrigation Cost", "Irrigation Frequency"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows - create separate row for each farmer-year combination
            int rowNum = 1;
            for (CottonFarmingReportDto farmer : cottonData) {
                // If farmer has year data, create a row for each year
                if (farmer.getYearData() != null && !farmer.getYearData().isEmpty()) {
                    for (CottonFarmingYearDataDto yearData : farmer.getYearData()) {
                        Row row = sheet.createRow(rowNum++);
                        populateCombinedFarmerYearRow(row, farmer, yearData, dataStyle);
                    }
                } else {
                    // If no year data, create a row with just farmer data
                    Row row = sheet.createRow(rowNum++);
                    populateCombinedFarmerYearRow(row, farmer, null, dataStyle);
                }
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Populate a row with combined farmer and year data
     */
    private void populateCombinedFarmerYearRow(Row row, CottonFarmingReportDto farmer, CottonFarmingYearDataDto yearData, CellStyle dataStyle) {
        int colIndex = 0;

        // Farmer basic information
        setCellValue(row, colIndex++, farmer.getFarmerId(), dataStyle);
        setCellValue(row, colIndex++, farmer.getFarmerTracenetCode(), dataStyle);
        setCellValue(row, colIndex++, farmer.getFarmerName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAge(), dataStyle);
        setCellValue(row, colIndex++, farmer.getGender(), dataStyle);
        setCellValue(row, colIndex++, farmer.getState(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDistrict(), dataStyle);
        setCellValue(row, colIndex++, farmer.getVillage(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCountryCode(), dataStyle);
        setCellValue(row, colIndex++, farmer.getMobileNumber(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreId(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreType(), dataStyle);
        setCellValue(row, colIndex++, farmer.getMaritalStatus(), dataStyle);
        setCellValue(row, colIndex++, farmer.getSpouseName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCaste(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHighestEducation(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHouseHoldSize(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLandSizeUnderCultivation(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLandMeasureType(), dataStyle);
        setCellValue(row, colIndex++, farmer.getOrganicStatus(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHerdSize(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAnyOtherIncomeGeneratingActivity(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHouseholdAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAgriculturalAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDairyAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getOtherAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCropsGrown(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCattleBreedTypes(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLoanAmount(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAgriculturalLoan(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDairyLoan(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLatLong(), dataStyle);
        setCellValue(row, colIndex++, farmer.getPartnerName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getProgramName(), dataStyle);

        // Year-specific cotton farming data
        if (yearData != null) {
            setCellValue(row, colIndex++, yearData.getYear(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMalesInHousehold(), dataStyle);
            setCellValue(row, colIndex++, yearData.getFemalesInHousehold(), dataStyle);
            setCellValue(row, colIndex++, yearData.getChildrenInHousehold(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSchoolGoingChildren(), dataStyle);
            setCellValue(row, colIndex++, yearData.getEarningMembers(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalLandholding(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPrimaryCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSecondaryCrops(), dataStyle);
            setCellValue(row, colIndex++, yearData.getNonOrganicCottonLand(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicCottonLand(), dataStyle);
            setCellValue(row, colIndex++, yearData.getYearsOrganicPractice(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCertificationStatus(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIrrigationSource(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCattleCount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getDrinkingWaterSource(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPreferredSellingPoint(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHasStorageSpace(), dataStyle);
            setCellValue(row, colIndex++, yearData.getReceivesAgroAdvisory(), dataStyle);
            setCellValue(row, colIndex++, yearData.getReceivedTraining(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMembershipInOrg(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMaintainsRecords(), dataStyle);
            setCellValue(row, colIndex++, yearData.getAnnualHouseholdIncome(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPrimaryIncomeSource(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPrimaryIncomeAmount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCertificationCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getAvgProductionPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCostOfCultivationPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicCottonQuantitySold(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSellingPricePerKg(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBioInputsCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPestManagementBioInputs(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBioFertilizerUsed(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPheromoneTrapsPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPheromoneTrapsPrice(), dataStyle);
            setCellValue(row, colIndex++, yearData.getYellowStickyTrapsPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getYellowStickyTrapsPrice(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBlueStickyTrapsPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBlueStickyTrapsPrice(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBirdPerchesPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIrrigationCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIrrigationCount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIrrigationMethod(), dataStyle);
            setCellValue(row, colIndex++, yearData.getFarmMachineryHired(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMachineryHiringCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getLocalLabourCostPerDay(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMigrantLabourCostPerDay(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWorkersForSowing(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWorkersForHarvesting(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHarvestingTime(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWeedingMethod(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWeedingCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMulchingCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTillageCount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTillageCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getLandPreparationCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicCottonSeedRate(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicCottonSeedVariety(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBorderCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getInterCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCoverCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTrapCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMulchingUsed(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMulchingType(), dataStyle);
            setCellValue(row, colIndex++, yearData.getStoragePrecautions(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHiredVehicleForTransport(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTransportationCostPerKg(), dataStyle);
            setCellValue(row, colIndex++, yearData.getRejectedQuantity(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPriceDiscoveryMechanism(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPaymentTransactionType(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCreditDays(), dataStyle);
            setCellValue(row, colIndex++, yearData.getGovtSchemeAvailed(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCropInsurance(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCropInsuranceCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHasKCC(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHasActiveBankAccount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCropRotationUsed(), dataStyle);
            setCellValue(row, colIndex++, yearData.getRotationCrops(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWaterTrackingDevices(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPumpCapacity(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBufferZone(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCropResidueUtilization(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWorkerPaymentMode(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWageGenderDifference(), dataStyle);
            setCellValue(row, colIndex++, yearData.getLabourRegister(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSafetyKitForWorkers(), dataStyle);
            setCellValue(row, colIndex++, yearData.getShelterAndWaterForWorkers(), dataStyle);
            setCellValue(row, colIndex++, yearData.getLavatoryForWorkers(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWomenInAgriOperations(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCommunityWaterHarvesting(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSoilMoistureMeterUsed(), dataStyle);

            // Calculated fields
            setCellValue(row, colIndex++, yearData.getTotalHHMembers(), dataStyle);
            setCellValue(row, colIndex++, yearData.getDependencyRatio(), dataStyle);
            setCellValue(row, colIndex++, yearData.getGenderRatio(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSchoolAttendanceRate(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalCottonLand(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicPercent(), dataStyle);
            setCellValue(row, colIndex++, yearData.getLandUsedForCotton(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIncomePerEarner(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOcIncome(), dataStyle);
            setCellValue(row, colIndex++, yearData.getProfitPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalCertificationCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalPTCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalYSTCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalBSTCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalPestMgmtCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalLabourCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMachineryCostTotal(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalIrrigationCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIrrigationFrequency(), dataStyle);
        } else {
            // If no year data, fill with empty cells for all year-specific columns
            // Count: 1 (year) + 5 (household) + 7 (land/crop) + 5 (infrastructure) + 4 (training) + 3 (income) + 5 (production) + 4 (bio inputs) + 7 (field ops) + 4 (seeds) + 3 (mulching) + 6 (transport) + 4 (govt schemes) + 4 (crop mgmt) + 19 (calculated) = 81 columns
            for (int i = 0; i < 81; i++) {
                setCellValue(row, colIndex++, null, dataStyle);
            }
        }
    }

    /**
     * Helper method to create header style
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        return headerStyle;
    }

    /**
     * Helper method to create data style
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        return dataStyle;
    }

    /**
     * Helper method to set cell value with proper type handling
     */
    private void setCellValue(Row row, int columnIndex, Object value, CellStyle style) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellStyle(style);

        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            cell.setCellValue(value.toString());
        }
    }
}
