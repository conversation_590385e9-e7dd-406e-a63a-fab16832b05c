package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.technoserve.udp.dto.*;
import org.technoserve.udp.entity.common.MasterDistrict;
import org.technoserve.udp.entity.program.*;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class ProgramFactSheetService {

  private final ProgramFactSheetRepository programFactSheetRepository;
  private final ProgramRepository programRepository;
  private final PartnerRepository partnerRepository;
  private final MasterDistrictRepository masterDistrictRepository;
  private final DistrictDataRepository districtDataRepository;

  @Transactional
  public ApiResponse createProgramFactSheet(ProgramFactSheetRequest request) {
    Program program = getProgramById(request.getProgramId());

    if (programFactSheetRepository.existsByProgram(program)) {
      throw new ConflictException("A Program Summary already exists for this Program.");
    }
    request.setProgramPhase(ProgramPhase.INACTIVE);
    List<Partner> partners = partnerRepository.findAllById(request.getProjectPartnerIds());
    ProgramFactSheet factSheet = buildProgramFactSheet(request, program, partners);
    program.setProgramFactSheet(programFactSheetRepository.save(factSheet));
    programRepository.save(program);

    saveOrUpdateDistrictData(request.getDistrictWiseData(), factSheet);

    return new ApiResponse("Program Summary created successfully", factSheet.getProgramFactSheetId());
  }

  @Transactional
  public ApiResponse updateProgramFactSheet(Long factSheetId, ProgramFactSheetRequest request) {
    ProgramFactSheet factSheet = getFactSheetById(factSheetId);
    updateProgramFactSheetFields(factSheet, request);

    programFactSheetRepository.save(factSheet);
    saveOrUpdateDistrictData(request.getDistrictWiseData(), factSheet);

    return new ApiResponse("Program Summary updated successfully", factSheet.getProgramFactSheetId());
  }


  public ProgramFactSheetResponse getProgramFactSheet(Long programId) {

    Optional<ProgramFactSheet> optFactSheet = programFactSheetRepository.findByProgram_ProgramId(programId);

    if(optFactSheet.isEmpty()) return new ProgramFactSheetResponse();

    ProgramFactSheet factSheet = optFactSheet.get();
    // Group districts by state
    Map<String, List<DistrictDataResponse>> stateWiseDistricts = factSheet.getDistrictWiseData().stream()
        .collect(Collectors.groupingBy(
            d -> d.getMasterDistrict().getMasterState().getStateName(),
            Collectors.mapping(d -> new DistrictDataResponse(
                d.getMasterDistrict().getMasterDistrictId(),
                d.getMasterDistrict().getDistrictName(),
                d.getNumberOfVillage()
            ), Collectors.toList())
        ));

    // Convert to StateDataResponse list
    List<StateDataResponse> stateDataResponses = stateWiseDistricts.entrySet().stream()
        .map(entry -> new StateDataResponse(entry.getKey(), entry.getValue()))
        .toList();

    return new ProgramFactSheetResponse(
        factSheet.getProgramFactSheetId(),
        factSheet.getProjectPartners(),
        factSheet.getTotalFarmers(),
        factSheet.getTotalFemaleFarmers(),
        stateDataResponses,
        factSheet.getStartYear(),
        factSheet.getEndYear(),
        factSheet.getProgramGoal(),
        factSheet.getProgramPhase()
    );
  }

  private Program getProgramById(Long programId) {
    return programRepository.findById(programId)
        .orElseThrow(() -> new ResourceNotFoundException("Program not found with ID: " + programId));
  }

  private ProgramFactSheet getFactSheetById(Long factSheetId) {
    return programFactSheetRepository.findById(factSheetId)
        .orElseThrow(() -> new ResourceNotFoundException("Program Summary not found with ID: " + factSheetId));
  }

  private void updateProgramFactSheetFields(ProgramFactSheet factSheet, ProgramFactSheetRequest request) {
    factSheet.setProgramGoal(request.getProgramGoal());
    factSheet.setTotalFarmers(request.getTotalFarmers());
    factSheet.setTotalFemaleFarmers(request.getTotalFemaleFarmers());
    factSheet.setStartYear(request.getStartYear());
    factSheet.setEndYear(request.getEndYear());
    factSheet.setProgramPhase(request.getProgramPhase());
    factSheet.setProjectPartners(partnerRepository.findAllById(request.getProjectPartnerIds()));
  }

  private void saveOrUpdateDistrictData(List<DistrictDataRequest> districtDataRequests, ProgramFactSheet factSheet) {
    if (districtDataRequests != null && !districtDataRequests.isEmpty()) {
      districtDataRepository.deleteByProgramFactSheet(factSheet);

      List<DistrictData> districtDataList = districtDataRequests.stream()
          .map(request -> convertToDistrictData(request, factSheet))
          .toList();

      districtDataRepository.saveAll(districtDataList);
    }
  }

  private ProgramFactSheet buildProgramFactSheet(ProgramFactSheetRequest request, Program program, List<Partner> partners) {
    return ProgramFactSheet.builder()
        .programGoal(request.getProgramGoal())
        .totalFarmers(request.getTotalFarmers())
        .totalFemaleFarmers(request.getTotalFemaleFarmers())
        .startYear(request.getStartYear())
        .endYear(request.getEndYear())
        .programPhase(request.getProgramPhase())
        .program(program)
        .projectPartners(partners)
        .build();
  }

  private DistrictData convertToDistrictData(DistrictDataRequest request, ProgramFactSheet factSheet) {
    MasterDistrict masterDistrict = masterDistrictRepository.findById(request.getMasterDistrictId())
        .orElseThrow(() -> new ResourceNotFoundException("Master District not found with ID: " + request.getMasterDistrictId()));

    return DistrictData.builder()
        .masterDistrict(masterDistrict)
        .numberOfVillage(request.getNumberOfVillage())
        .programFactSheet(factSheet)
        .build();
  }


}
