package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.technoserve.udp.dto.*;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.*;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.ProgramLogFrameRepository;
import org.technoserve.udp.repository.ProgramRepository;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ProgramLogFrameService {

  private final ProgramRepository programRepository;

  private final ProgramLogFrameRepository programLogFrameRepository;

  @Transactional
  public ApiResponse createFullLogFrame(ProgramLogFrameRequest request) {
    Program program = programRepository.findById(request.getProgramId())
        .orElseThrow(() -> new ResourceNotFoundException("Program not found"));

    if (program.getProgramLogFrame() != null) {
      throw new ConflictException("Program log frame already exists for Program id " + program.getProgramId());
    }

    ProgramLogFrame logFrame = new ProgramLogFrame();
    List<Dimension> dimensions = request.getDimensions().stream()
        .map(this::mapDimensionForCreate)
        .toList();
    dimensions.forEach(d -> d.setProgramLogFrame(logFrame));
    logFrame.setDimensions(dimensions);
    ProgramLogFrame savedProgramLogFrame = programLogFrameRepository.save(logFrame);
    program.setProgramLogFrame(savedProgramLogFrame);
    programRepository.save(program);
    return new ApiResponse("LogFrame created successfully",savedProgramLogFrame.getProgramLogFrameId());
  }

  // UPDATE endpoint: uses the ProgramLogFrame id as a path variable.
  @Transactional
  public ApiResponse updateFullLogFrame(Long programLogFrameId, ProgramLogFrameRequest request) {

    ProgramLogFrame logFrame = programLogFrameRepository.findById(programLogFrameId)
        .orElseThrow(() -> new ResourceNotFoundException("Program Fact Sheet not found"));
    Program program = logFrame.getProgram();

    logFrame.setDimensions(updateDimensions(logFrame.getDimensions(), request.getDimensions(), logFrame));

    ProgramLogFrame savedLogFrame = programLogFrameRepository.save(logFrame);
    program.setProgramLogFrame(savedLogFrame);
    programRepository.save(program);

    return new ApiResponse("LogFrame updated successfully",programLogFrameId);

  }


  public ProgramLogFrameDto getProgramLogFrameByProgramId(Long programId) {
    Optional<ProgramLogFrame> logFrame = programLogFrameRepository.findByProgram_ProgramId(programId);
    return logFrame.map(this::mapToProgramLogFrameDto).orElseGet(ProgramLogFrameDto::new);
  }




  private ProgramLogFrameDto mapToProgramLogFrameDto(ProgramLogFrame logFrame) {
    ProgramLogFrameDto dto = new ProgramLogFrameDto();
    dto.setProgramId(logFrame.getProgram().getProgramId());
    dto.setProgramLogFrameId(logFrame.getProgramLogFrameId());
    List<DimensionDto> dimensions = logFrame.getDimensions().stream()
        .filter(d -> d.getStatus() == Status.CREATED)
        .map(this::mapDimensionToDto)
        .toList();
    dto.setDimensions(dimensions);
    return dto;
  }

  private DimensionDto mapDimensionToDto(Dimension dimension) {
    DimensionDto dto = new DimensionDto();
    dto.setDimensionId(dimension.getDimensionId());
    dto.setType(dimension.getType().name());
    List<ObjectiveDto> objectives = dimension.getObjectives().stream()
        .filter(o -> o.getStatus() == Status.CREATED)
        .map(this::mapObjectiveToDto)
        .toList();
    dto.setObjectives(objectives);
    return dto;
  }

  private ObjectiveDto mapObjectiveToDto(Objective objective) {
    ObjectiveDto dto = new ObjectiveDto();
    dto.setObjectiveId(objective.getObjectiveId());
    dto.setKpiName(objective.getKpiName());
    dto.setLogoURL(objective.getLogoURL());
    dto.setOutcomeIndicator(objective.getOutcomeIndicator());
    dto.setUnitOfMeasurement(objective.getUnitOfMeasurement());
    dto.setEndOfProgramTarget(objective.getEndOfProgramTarget());
    List<OutputIndicatorDto> outputs = objective.getOutputIndicators().stream()
        .filter(o -> o.getStatus() == Status.CREATED)
        .map(this::mapOutputIndicatorToDto)
        .toList();
    dto.setOutputIndicators(outputs);
    return dto;
  }

  private OutputIndicatorDto mapOutputIndicatorToDto(OutputIndicator outputIndicator) {
    OutputIndicatorDto dto = new OutputIndicatorDto();
    dto.setOutputIndicatorId(outputIndicator.getOutputIndicatorId());
    dto.setOutputIndicatorName(outputIndicator.getOutputIndicatorName());
    dto.setUnitOfMeasurement(outputIndicator.getUnitOfMeasurement());
    dto.setYyTarget(outputIndicator.getYyTarget());
    List<KeyActivityDto> keyActivities = outputIndicator.getKeyActivities().stream()
        .filter(k -> k.getStatus() == Status.CREATED)
        .map(this::mapKeyActivityToDto)
        .toList();
    dto.setKeyActivities(keyActivities);
    return dto;
  }

  private KeyActivityDto mapKeyActivityToDto(KeyActivity keyActivity) {
    KeyActivityDto dto = new KeyActivityDto();
    dto.setKeyActivityId(keyActivity.getKeyActivityId());
    dto.setKeyActivityName(keyActivity.getKeyActivityName());
    List<SubActivityDto> subActivities = keyActivity.getSubActivities().stream()
        .filter(s -> s.getStatus() == Status.CREATED)
        .map(this::mapSubActivityToDto)
        .toList();
    dto.setSubActivities(subActivities);
    return dto;
  }

  private SubActivityDto mapSubActivityToDto(SubActivity subActivity) {
    SubActivityDto dto = new SubActivityDto();
    dto.setSubActivityId(subActivity.getSubActivityId());
    dto.setSubActivityName(subActivity.getSubActivityName());
    List<MicroActivityDto> microActivities = subActivity.getMicroActivities().stream()
        .filter(m -> m.getStatus() == Status.CREATED)
        .map(this::mapMicroActivityToDto)
        .toList();
    dto.setMicroActivities(microActivities);
    return dto;
  }

  private MicroActivityDto mapMicroActivityToDto(MicroActivity microActivity) {
    MicroActivityDto dto = new MicroActivityDto();
    dto.setMicroActivityId(microActivity.getMicroActivityId());
    dto.setMicroActivityName(microActivity.getMicroActivityName());
    dto.setTools(microActivity.getTools());
    dto.setMicroActivityType(microActivity.getMicroActivityType().name());
    dto.setFrequency(microActivity.getFrequency().name());
    dto.setResponsibleParty(microActivity.getResponsibleParty());
    dto.setAccountableParty(microActivity.getAccountableParty());
    dto.setConsultedParty(microActivity.getConsultedParty());
    dto.setInformedParty(microActivity.getInformedParty());
    return dto;
  }


  private Dimension mapDimensionForCreate(DimensionRequest req) {
    Dimension dimension = new Dimension();
    dimension.setType(DimensionType.valueOf(req.getType()));
    dimension.setStatus(Status.CREATED);
    List<Objective> objectives = req.getObjectives().stream()
        .map(this::mapObjectiveForCreate)
        .toList();
    objectives.forEach(o -> o.setDimension(dimension));
    dimension.setObjectives(objectives);
    return dimension;
  }

  private Objective mapObjectiveForCreate(ObjectiveRequest req) {
    Objective objective = new Objective();
    objective.setKpiName(req.getKpiName());
    objective.setLogoURL(req.getLogoURL());
    objective.setOutcomeIndicator(req.getOutcomeIndicator());
    objective.setUnitOfMeasurement(req.getUnitOfMeasurement());
    objective.setEndOfProgramTarget(req.getEndOfProgramTarget());
    objective.setStatus(Status.CREATED);
    List<OutputIndicator> outputIndicators = req.getOutputIndicators().stream()
        .map(this::mapOutputIndicatorForCreate)
        .toList();
    outputIndicators.forEach(o -> o.setObjective(objective));
    objective.setOutputIndicators(outputIndicators);
    return objective;
  }

  private OutputIndicator mapOutputIndicatorForCreate(OutputIndicatorRequest req) {
    OutputIndicator outputIndicator = new OutputIndicator();
    outputIndicator.setOutputIndicatorName(req.getOutputIndicatorName());
    outputIndicator.setUnitOfMeasurement(req.getUnitOfMeasurement());
    outputIndicator.setYyTarget(req.getYyTarget());
    outputIndicator.setStatus(Status.CREATED);
    List<KeyActivity> keyActivities = req.getKeyActivities().stream()
        .map(this::mapKeyActivityForCreate)
        .toList();
    keyActivities.forEach(k -> k.setOutputIndicator(outputIndicator));
    outputIndicator.setKeyActivities(keyActivities);
    return outputIndicator;
  }

  private KeyActivity mapKeyActivityForCreate(KeyActivityRequest req) {
    KeyActivity keyActivity = new KeyActivity();
    keyActivity.setKeyActivityName(req.getKeyActivityName());
    keyActivity.setStatus(Status.CREATED);
    List<SubActivity> subActivities = req.getSubActivities().stream()
        .map(this::mapSubActivityForCreate)
        .toList();
    subActivities.forEach(s -> s.setKeyActivity(keyActivity));
    keyActivity.setSubActivities(subActivities);
    return keyActivity;
  }

  private SubActivity mapSubActivityForCreate(SubActivityRequest req) {
    SubActivity subActivity = new SubActivity();
    subActivity.setSubActivityName(req.getSubActivityName());
    subActivity.setStatus(Status.CREATED);
    List<MicroActivity> microActivities = req.getMicroActivities().stream()
        .map(this::mapMicroActivityForCreate)
        .toList();
    microActivities.forEach(m -> m.setSubActivity(subActivity));
    subActivity.setMicroActivities(microActivities);
    return subActivity;
  }

  private MicroActivity mapMicroActivityForCreate(MicroActivityRequest req) {
    MicroActivity microActivity = new MicroActivity();
    microActivity.setMicroActivityName(req.getMicroActivityName());
    microActivity.setTools(req.getTools());
    microActivity.setMicroActivityType(MicroActivityType.valueOf(req.getMicroActivityType()));
    microActivity.setFrequency(MicroActivityFrequency.valueOf(req.getFrequency()));
    microActivity.setResponsibleParty(req.getResponsibleParty());
    microActivity.setAccountableParty(req.getAccountableParty());
    microActivity.setConsultedParty(req.getConsultedParty());
    microActivity.setInformedParty(req.getInformedParty());
    microActivity.setStatus(Status.CREATED);
    return microActivity;
  }

  // ===== Mapping methods for Update (merge incoming data with existing nested entities) =====

  private List<Dimension> updateDimensions(List<Dimension> existingDimensions, List<DimensionRequest> newDimensions, ProgramLogFrame logFrame) {
    Map<Long, Dimension> existingMap = existingDimensions == null ? new HashMap<>() :
        existingDimensions.stream()
            .filter(d -> d.getDimensionId() != null)
            .collect(Collectors.toMap(Dimension::getDimensionId, d -> d));
    if (existingDimensions != null) {
      existingDimensions.forEach(d -> d.setStatus(Status.DELETED));
    }
    List<Dimension> result = new ArrayList<>();
    for (DimensionRequest req : newDimensions) {
      Dimension dimension;
      if (req.getDimensionId() != null && existingMap.containsKey(req.getDimensionId())) {
        dimension = existingMap.get(req.getDimensionId());
        dimension.setType(DimensionType.valueOf(req.getType()));
      } else {
        dimension = new Dimension();
        dimension.setType(DimensionType.valueOf(req.getType()));
      }
      dimension.setStatus(Status.CREATED);
      dimension.setProgramLogFrame(logFrame);
      dimension.setObjectives(updateObjectives(dimension.getObjectives(), req.getObjectives(), dimension));
      result.add(dimension);
    }
    return result;
  }

  private List<Objective> updateObjectives(List<Objective> existingObjectives, List<ObjectiveRequest> newObjectives, Dimension dimension) {
    Map<Long, Objective> existingMap = existingObjectives == null ? new HashMap<>() :
        existingObjectives.stream()
            .filter(o -> o.getObjectiveId() != null)
            .collect(Collectors.toMap(Objective::getObjectiveId, o -> o));
    if (existingObjectives != null) {
      existingObjectives.forEach(o -> o.setStatus(Status.DELETED));
    }
    List<Objective> result = new ArrayList<>();
    for (ObjectiveRequest req : newObjectives) {
      Objective objective;
      if (req.getObjectiveId() != null && existingMap.containsKey(req.getObjectiveId())) {
        objective = existingMap.get(req.getObjectiveId());
        objective.setKpiName(req.getKpiName());
        objective.setLogoURL(req.getLogoURL());
        objective.setOutcomeIndicator(req.getOutcomeIndicator());
        objective.setUnitOfMeasurement(req.getUnitOfMeasurement());
        objective.setEndOfProgramTarget(req.getEndOfProgramTarget());
      } else {
        objective = new Objective();
        objective.setKpiName(req.getKpiName());
        objective.setLogoURL(req.getLogoURL());
        objective.setOutcomeIndicator(req.getOutcomeIndicator());
        objective.setUnitOfMeasurement(req.getUnitOfMeasurement());
        objective.setEndOfProgramTarget(req.getEndOfProgramTarget());
      }
      objective.setStatus(Status.CREATED);
      objective.setDimension(dimension);
      objective.setOutputIndicators(updateOutputIndicators(objective.getOutputIndicators(), req.getOutputIndicators(), objective));
      result.add(objective);
    }
    return result;
  }

  private List<OutputIndicator> updateOutputIndicators(List<OutputIndicator> existingOutputIndicators, List<OutputIndicatorRequest> newOutputIndicators, Objective objective) {
    Map<Long, OutputIndicator> existingMap = existingOutputIndicators == null ? new HashMap<>() :
        existingOutputIndicators.stream()
            .filter(o -> o.getOutputIndicatorId() != null)
            .collect(Collectors.toMap(OutputIndicator::getOutputIndicatorId, o -> o));
    if (existingOutputIndicators != null) {
      existingOutputIndicators.forEach(o -> o.setStatus(Status.DELETED));
    }
    List<OutputIndicator> result = new ArrayList<>();
    for (OutputIndicatorRequest req : newOutputIndicators) {
      OutputIndicator outputIndicator;
      if (req.getOutputIndicatorId() != null && existingMap.containsKey(req.getOutputIndicatorId())) {
        outputIndicator = existingMap.get(req.getOutputIndicatorId());
        outputIndicator.setOutputIndicatorName(req.getOutputIndicatorName());
        outputIndicator.setUnitOfMeasurement(req.getUnitOfMeasurement());
        outputIndicator.setYyTarget(req.getYyTarget());
      } else {
        outputIndicator = new OutputIndicator();
        outputIndicator.setOutputIndicatorName(req.getOutputIndicatorName());
        outputIndicator.setUnitOfMeasurement(req.getUnitOfMeasurement());
        outputIndicator.setYyTarget(req.getYyTarget());
      }
      outputIndicator.setStatus(Status.CREATED);
      outputIndicator.setObjective(objective);
      outputIndicator.setKeyActivities(updateKeyActivities(outputIndicator.getKeyActivities(), req.getKeyActivities(), outputIndicator));
      result.add(outputIndicator);
    }
    return result;
  }

  private List<KeyActivity> updateKeyActivities(List<KeyActivity> existingKeyActivities, List<KeyActivityRequest> newKeyActivities, OutputIndicator outputIndicator) {
    Map<Long, KeyActivity> existingMap = existingKeyActivities == null ? new HashMap<>() :
        existingKeyActivities.stream()
            .filter(k -> k.getKeyActivityId() != null)
            .collect(Collectors.toMap(KeyActivity::getKeyActivityId, k -> k));
    if (existingKeyActivities != null) {
      existingKeyActivities.forEach(k -> k.setStatus(Status.DELETED));
    }
    List<KeyActivity> result = new ArrayList<>();
    for (KeyActivityRequest req : newKeyActivities) {
      KeyActivity keyActivity;
      if (req.getKeyActivityId() != null && existingMap.containsKey(req.getKeyActivityId())) {
        keyActivity = existingMap.get(req.getKeyActivityId());
        keyActivity.setKeyActivityName(req.getKeyActivityName());
      } else {
        keyActivity = new KeyActivity();
        keyActivity.setKeyActivityName(req.getKeyActivityName());
      }
      keyActivity.setStatus(Status.CREATED);
      keyActivity.setOutputIndicator(outputIndicator);
      keyActivity.setSubActivities(updateSubActivities(keyActivity.getSubActivities(), req.getSubActivities(), keyActivity));
      result.add(keyActivity);
    }
    return result;
  }

  private List<SubActivity> updateSubActivities(List<SubActivity> existingSubActivities, List<SubActivityRequest> newSubActivities, KeyActivity keyActivity) {
    Map<Long, SubActivity> existingMap = existingSubActivities == null ? new HashMap<>() :
        existingSubActivities.stream()
            .filter(s -> s.getSubActivityId() != null)
            .collect(Collectors.toMap(SubActivity::getSubActivityId, s -> s));
    if (existingSubActivities != null) {
      existingSubActivities.forEach(s -> s.setStatus(Status.DELETED));
    }
    List<SubActivity> result = new ArrayList<>();
    for (SubActivityRequest req : newSubActivities) {
      SubActivity subActivity;
      if (req.getSubActivityId() != null && existingMap.containsKey(req.getSubActivityId())) {
        subActivity = existingMap.get(req.getSubActivityId());
        subActivity.setSubActivityName(req.getSubActivityName());
      } else {
        subActivity = new SubActivity();
        subActivity.setSubActivityName(req.getSubActivityName());
      }
      subActivity.setStatus(Status.CREATED);
      subActivity.setKeyActivity(keyActivity);
      subActivity.setMicroActivities(updateMicroActivities(subActivity.getMicroActivities(), req.getMicroActivities(), subActivity));
      result.add(subActivity);
    }
    return result;
  }

  private List<MicroActivity> updateMicroActivities(List<MicroActivity> existingMicroActivities, List<MicroActivityRequest> newMicroActivities, SubActivity subActivity) {
    Map<Long, MicroActivity> existingMap = existingMicroActivities == null ? new HashMap<>() :
        existingMicroActivities.stream()
            .filter(m -> m.getMicroActivityId() != null)
            .collect(Collectors.toMap(MicroActivity::getMicroActivityId, m -> m));
    if (existingMicroActivities != null) {
      existingMicroActivities.forEach(m -> m.setStatus(Status.DELETED));
    }
    List<MicroActivity> result = new ArrayList<>();
    for (MicroActivityRequest req : newMicroActivities) {
      MicroActivity microActivity;
      if (req.getMicroActivityId() != null && existingMap.containsKey(req.getMicroActivityId())) {
        microActivity = existingMap.get(req.getMicroActivityId());
        microActivity.setMicroActivityName(req.getMicroActivityName());
        microActivity.setTools(req.getTools());
        microActivity.setMicroActivityType(MicroActivityType.valueOf(req.getMicroActivityType()));
        microActivity.setFrequency(MicroActivityFrequency.valueOf(req.getFrequency()));
        microActivity.setResponsibleParty(req.getResponsibleParty());
        microActivity.setAccountableParty(req.getAccountableParty());
        microActivity.setConsultedParty(req.getConsultedParty());
        microActivity.setInformedParty(req.getInformedParty());
      } else {
        microActivity = new MicroActivity();
        microActivity.setMicroActivityName(req.getMicroActivityName());
        microActivity.setTools(req.getTools());
        microActivity.setMicroActivityType(MicroActivityType.valueOf(req.getMicroActivityType()));
        microActivity.setFrequency(MicroActivityFrequency.valueOf(req.getFrequency()));
        microActivity.setResponsibleParty(req.getResponsibleParty());
        microActivity.setAccountableParty(req.getAccountableParty());
        microActivity.setConsultedParty(req.getConsultedParty());
        microActivity.setInformedParty(req.getInformedParty());
      }
      microActivity.setStatus(Status.CREATED);
      microActivity.setSubActivity(subActivity);
      result.add(microActivity);
    }
    return result;
  }
}
