package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;
import org.technoserve.udp.entity.auth.Role;
import org.technoserve.udp.entity.auth.UserEntity;
import org.technoserve.udp.entity.auth.UserStatus;
import org.technoserve.udp.exception.InactiveUserException;
import org.technoserve.udp.exception.UserNotFoundException;
import org.technoserve.udp.repository.UserRepository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CustomUserRoleService {

    private final UserRepository userRepository;

    /**
     * Retrieves custom roles for the given user's email and converts them to GrantedAuthorities.
     *
     * @param email the user's email (which is the primary key in our User entity)
     * @return a collection of GrantedAuthority representing the user's roles
     * @throws UserNotFoundException if the user is not found
     * @throws InactiveUserException if the user is inactive or suspended
     */
    public Collection<? extends GrantedAuthority> getAuthorities(String email) {
        return userRepository.findByEmailWithRoles(email)
                .map(user -> {
                    if (user.getStatus() != UserStatus.ACTIVE) {
                        throw new InactiveUserException(email, user.getStatus());
                    }

                    List<Role> rolesCopy = new ArrayList<>(user.getRoles()); // Clone roles
                    return rolesCopy.stream()
                            .map(role -> new SimpleGrantedAuthority(role.getName()))
                            .collect(Collectors.toList());
                })
                .orElseThrow(() -> new UserNotFoundException(email));
    }
}
