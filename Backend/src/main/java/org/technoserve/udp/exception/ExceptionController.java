package org.technoserve.udp.exception;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import java.io.IOException;

@Controller
public class ExceptionController implements ErrorController {

    private static final Logger logger = LoggerFactory.getLogger(ExceptionController.class);

    @GetMapping("/error")
    public void handleError(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // Get HTTP Status Code
        Integer statusCode = (Integer) request.getAttribute("jakarta.servlet.error.status_code");
        Exception exception = (Exception) request.getAttribute("jakarta.servlet.error.exception");
        String requestURL = request.getRequestURL().toString();
        String url= requestURL.substring(0,requestURL.indexOf("/udp/")) ;
        // Log the error
        logger.error("Error occurred: Status = {}, Exception = {}", statusCode, exception);
        if (exception != null ) {


            // Check if exception is UserNotFoundException
            if (exception instanceof UserNotFoundException userNotFoundException) {
                String email = userNotFoundException.getEmail(); // Assuming the exception holds the email
                logger.warn("User not found, redirecting to /admin-contact?email={}", email);
                response.sendRedirect(url+"/admin-contact?email=" + email);
                return;
            }

            // Check if exception is InactiveUserException
            if (exception instanceof InactiveUserException inactiveUserException) {
                String email = inactiveUserException.getEmail(); // Assuming the exception holds the email
                String status = inactiveUserException.getStatus().name(); // Assuming getStatus() returns enum
                logger.warn("User is inactive, redirecting to /inactive-user?email={}&status={}", email, status);
                response.sendRedirect(url+"/admin-contact?email=" + email + "&status=" + status);
                return;
            }
        }
// Check if exception is ResourceNotFoundException
        if (exception instanceof ResourceNotFoundException resourceNotFoundException) {

            logger.warn("resourceNotFoundException, redirecting to /resource-not-found");
            response.sendRedirect(url+"/resource-not-found");
            return;
        }
        // If unknown error, send default error page
        logger.warn("Unknown error occurred, redirecting to /general-error");
        response.sendRedirect(url+"/internal-servererror");
    }
}
