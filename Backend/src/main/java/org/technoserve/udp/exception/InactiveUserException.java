package org.technoserve.udp.exception;

import org.technoserve.udp.entity.auth.UserStatus;

public class InactiveUserException extends RuntimeException {
    private final String email;
    private final UserStatus status;

    public InactiveUserException(String email, UserStatus status) {
        super("User is inactive: " + email + " with status: " + status);
        this.email = email;
        this.status = status;
    }

    public String getEmail() {
        return email;
    }

    public UserStatus getStatus() {
        return status;
    }
}
