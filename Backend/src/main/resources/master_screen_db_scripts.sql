INSERT INTO public.master_screen(
	name)
	VALUES ('Value Chain'),('Program'),('Fund Code'),('Program Summary'),('Log Frame'),('Budget'),('Team');

-- Properties for value chain
INSERT INTO public.property(
	 label_name, name,  master_screen_id)
	select 'Name','name',(select id from master_screen where name ='Value Chain');

INSERT INTO public.property(
	 label_name, name,  master_screen_id)
	select 'Picture Image','logoURL',(select id from master_screen where name ='Value Chain');

-- Properties for Program
INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Name', 'name', (SELECT id FROM master_screen WHERE name = 'Program');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Picture Image', 'logoURL', (SELECT id FROM master_screen WHERE name = 'Program');



-- Properties for Team
INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Name', 'name', (SELECT id FROM master_screen WHERE name = 'Team');

INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Title', 'title', (SELECT id FROM master_screen WHERE name = 'Team');

INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Rank', 'rank', (SELECT id FROM master_screen WHERE name = 'Team');


-- Properties for Fund Code
INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Sponsor Name', 'Sponsor.name', (SELECT id FROM master_screen WHERE name = 'Fund Code');

INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Fund Code', 'fundCode', (SELECT id FROM master_screen WHERE name = 'Fund Code');

INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Currency', 'currency', (SELECT id FROM master_screen WHERE name = 'Fund Code');

INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Contribution %', 'contribution', (SELECT id FROM master_screen WHERE name = 'Fund Code');

INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Budget Allocation', 'budgetAllocation', (SELECT id FROM master_screen WHERE name = 'Fund Code');

INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'Implementation Budget', 'implementationBudget', (SELECT id FROM master_screen WHERE name = 'Fund Code');

INSERT INTO public.property(label_name, name, master_screen_id)
SELECT 'PMU Budget', 'pmuBudget', (SELECT id FROM master_screen WHERE name = 'Fund Code');


-- ----------------------
--Properties for Log Frame
-- ----------------------

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Dimension', 'dimensions.objectives.dimension', (SELECT id FROM master_screen WHERE name = 'Log frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Objective (KPIs)', 'dimensions.objectives.objectiveKPIs', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Outcome Indicators', 'dimensions.objectives.outcomeIndicators', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Unit of Measurement', 'dimensions.objectives.unitOfMeasurement', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'End of Program Target', 'dimensions.objectives.endOfProgramTarget', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Output Indicators', 'dimensions.objectives.outputIndicators', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Unit of Measurement', 'dimensions.objectives.outputIndicators.unitOfMeasurement', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Y/Y Target', 'dimensions.objectives.outputIndicators.yearOnYearTarget', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Key Program Activities', 'keyProgramActivities', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Sub-Activities', 'subActivities', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Micro-Activities', 'microActivities', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Micro-Activity Type', 'microActivityType', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Tools', 'tools', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Frequency', 'frequency', (SELECT id FROM master_screen WHERE name = 'Log Frame');


INSERT INTO property(label_name, name, master_screen_id)
SELECT 'R (Responsible)', 'responsible', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'A (Accountable)', 'accountable', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'C (Consulted)', 'consulted', (SELECT id FROM master_screen WHERE name = 'Log Frame');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'I (Informed)', 'informed', (SELECT id FROM master_screen WHERE name = 'Log Frame');

-------------------------
-- Properties for Budget
--------------------------
INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Frequency', 'frequency', id FROM master_screen WHERE name = 'Budget';

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Unit', 'unit', id FROM master_screen WHERE name = 'Budget';

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Unit Cost', 'unitCost', id FROM master_screen WHERE name = 'Budget';

INSERT INTO property(label_name, name, master_screen_id)
SELECT '# Unit', 'noOfUnits', id FROM master_screen WHERE name = 'Budget';

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Total Cost', 'totalCost', id FROM master_screen WHERE name = 'Budget';

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Total Amount', 'BudgetYearWise.totalAmount', id FROM master_screen WHERE name = 'Budget';

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Year IP', 'BudgetYearWise.ip', id FROM master_screen WHERE name = 'Budget';

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Year Farmer Contribution', 'BudgetYearWise.farmerContribution', id FROM master_screen WHERE name = 'Budget';

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Year Sponsor Contribution', 'BudgetYearWise.projectSponsorContribution', id FROM master_screen WHERE name = 'Budget';



-- Permission for Value Chain

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'View','VIEW',(select id from master_screen where name ='Value Chain');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Create','CREATE',(select id from master_screen where name ='Value Chain');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Edit','EDIT',(select id from master_screen where name ='Value Chain');


-- Endpoints for value chain
INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('GET','/value-chain/list');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('POST','/value-chain/create');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('PUT','/value-chain/update/*');

INSERT INTO public.endpoints(
      http_method, pattern)
     VALUES ('DELETE','/value-chain/delete/*');

INSERT INTO public.endpoints(
      http_method, pattern)
     VALUES ('GET','/value-chain/types');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('POST','/gcp/upload-logo');




INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Value Chain')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/gcp/upload-logo') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Value Chain')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/value-chain/create') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Value Chain')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/value-chain/update/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Value Chain')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'DELETE' and pattern ='/value-chain/delete/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Value Chain')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/value-chain/list') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Value Chain')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/value-chain/types') ;


-- Program

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'View','VIEW',(select id from master_screen where name ='Program');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Create','CREATE',(select id from master_screen where name ='Program');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Edit','EDIT',(select id from master_screen where name ='Program');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Review','REVIEW',(select id from master_screen where name ='Program');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Approve','APPROVE',(select id from master_screen where name ='Program');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('POST','/program/create');


INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('PUT','/program/update/*');

INSERT INTO public.endpoints(
     http_method, pattern)
    VALUES ('DELETE','/program/delete/*');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('GET','/value-chain/*/program-list');

INSERT INTO public.endpoints(
     http_method, pattern)
    VALUES ('PUT','/program/*/submit');

INSERT INTO public.endpoints(
     http_method, pattern)
    VALUES ('PUT','/program/*/review');

INSERT INTO public.endpoints(
     http_method, pattern)
    VALUES ('PUT','/program/*/approve');

INSERT INTO public.endpoints(
     http_method, pattern)
    VALUES ('PUT','/program/*/reject');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('GET','/master-data/states/*');

 INSERT INTO public.endpoints(
     	 http_method, pattern)
     	VALUES ('GET','/partner/list');

 INSERT INTO public.endpoints(
     	 http_method, pattern)
     	VALUES ('GET','/program-log-frame/micro-activity-types');

 INSERT INTO public.endpoints(
     	 http_method, pattern)
     	VALUES ('GET','/program-log-frame/micro-activity-frequencies');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('GET','/program-fact-sheet/program-phases');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('GET','/sponsor/list');

INSERT INTO public.endpoints(
     http_method, pattern)
    VALUES ('PUT','/program/*/program-phase');

INSERT INTO public.endpoints(
     http_method, pattern)
    VALUES ('GET','/master-data/currencies');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/program/create') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/update/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'DELETE' and pattern ='/program/delete/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/*/program-phase') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/value-chain/*/program-list') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/*/submit') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'REVIEW' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/*/review') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'REVIEW' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/*/reject') ;

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'REVIEW'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/master-data/states/*');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'REVIEW'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/partner/list');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'REVIEW'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/program-log-frame/micro-activity-types');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'REVIEW'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/program-log-frame/micro-activity-frequencies');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'REVIEW'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/program-fact-sheet/program-phases');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'REVIEW'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/sponsor/list');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'REVIEW'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/master-data/currencies');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'APPROVE' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/*/approve') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program')
	and name= 'APPROVE' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/*/reject') ;


INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'APPROVE'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/master-data/states/*');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'APPROVE'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/partner/list');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'APPROVE'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/program-log-frame/micro-activity-types');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'APPROVE'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/program-log-frame/micro-activity-frequencies');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'APPROVE'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/program-fact-sheet/program-phases');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'APPROVE'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/sponsor/list');

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Program')
          AND name = 'APPROVE'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/master-data/currencies');

--Fund Code

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'View','VIEW',(select id from master_screen where name ='Fund Code');

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'Create','CREATE',(select id from master_screen where name ='Fund Code');

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'Edit','EDIT',(select id from master_screen where name ='Fund Code');



INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('POST','/fund-code/create');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('PUT','/fund-code/update/*');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('DELETE','/fund-code/delete/*');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('GET','/program/*/fund-code-list');


INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('POST','/sponsor/create');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('PUT','/sponsor/update/*');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('DELETE','/sponsor/delete/*');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Fund Code')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/fund-code-list') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Fund Code')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/sponsor/list') ;

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Fund Code')
          AND name = 'VIEW'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/master-data/currencies');



INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Fund Code')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/fund-code/create') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Fund Code')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/sponsor/list') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Fund Code')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/sponsor/create') ;

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Fund Code')
          AND name = 'CREATE'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/master-data/currencies');



INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Fund Code')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/fund-code/update/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Fund Code')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'DELETE' and pattern ='/fund-code/delete/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Fund Code')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/sponsor/list') ;

INSERT INTO public.permission_endpoints(permission_id, endpoint_id)
SELECT (SELECT id FROM permissions
        WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Fund Code')
          AND name = 'EDIT'),
       (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/master-data/currencies');

-- Fact Sheet/ Program Summary

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'View','VIEW',(select id from master_screen where name ='Program Summary');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Create','CREATE',(select id from master_screen where name ='Program Summary');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Edit','EDIT',(select id from master_screen where name ='Program Summary');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('POST','/partner/create');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('PUT','/partner/update/*');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('DELETE','/partner/delete/*');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('GET','/master-data/districts/*');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('POST','/master-data/districts/create');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('POST','/program-fact-sheet/create');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('PUT','/program-fact-sheet/update/*');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('GET','/program/*/fact-sheet');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/partner/list') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/partner/create') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/master-data/states/*') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/master-data/districts/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/master-data/districts/create') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/program-fact-sheet/create') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program-fact-sheet/update/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-fact-sheet/program-phases') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/master-data/states/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-fact-sheet/program-phases') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/partner/list') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/master-data/states/*') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/fact-sheet') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Program Summary')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-fact-sheet/program-phases') ;


INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Project Partners', 'programFactSheet.projectPartners', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Total Farmers', 'programFactSheet.totalFarmers', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Total Female Farmers', 'programFactSheet.totalFemaleFarmers', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Start Year', 'programFactSheet.startYear', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'End Year', 'programFactSheet.endYear', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Program Goal', 'programFactSheet.programGoal', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Program Phase', 'programFactSheet.programPhase', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'State', 'programFactSheet.districtWiseData.masterDistrict.masterState.stateName', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'District', 'programFactSheet.districtWiseData.masterDistrict.districtName', (SELECT id FROM master_screen WHERE name = 'Program Summary');

INSERT INTO property(label_name, name, master_screen_id)
SELECT 'Village', 'programFactSheet.districtWiseData.numberOfVillage', (SELECT id FROM master_screen WHERE name = 'Program Summary');


--Log Frame

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'View','VIEW',(select id from master_screen where name ='Log Frame');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Create','CREATE',(select id from master_screen where name ='Log Frame');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Edit','EDIT',(select id from master_screen where name ='Log Frame');



INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('POST','/program-log-frame/create');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('PUT','/program-log-frame/update/*');

INSERT INTO public.endpoints(
    	 http_method, pattern)
    	VALUES ('GET','/program/*/log-frame');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/program-log-frame/create') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-log-frame/micro-activity-types') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-log-frame/micro-activity-frequencies') ;



INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program-log-frame/update/*') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-log-frame/micro-activity-types') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-log-frame/micro-activity-frequencies') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/log-frame') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-log-frame/micro-activity-types') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Log Frame')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program-log-frame/micro-activity-frequencies') ;


-- Permission for Budget

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'View','VIEW',(select id from master_screen where name ='Budget');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Create','CREATE',(select id from master_screen where name ='Budget');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Edit','EDIT',(select id from master_screen where name ='Budget');


-- Endpoints for value chain
INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('POST','/micro-activities/*/budget');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('PUT','/micro-activities/*/budget');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('GET','/program/*/micro-activities/financial');


INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('GET','/micro-activities/budget-frequencies');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Budget')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/micro-activities/financial') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Budget')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/micro-activities/*/budget') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Budget')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/micro-activities/*/budget') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Budget')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/micro-activities/budget-frequencies') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Budget')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/micro-activities/budget-frequencies') ;



-- Permission for Team

INSERT INTO public.permissions(
	 label_name, name,  master_screen_id)
	select 'View','VIEW',(select id from master_screen where name ='Team');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Create','CREATE',(select id from master_screen where name ='Team');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Edit','EDIT',(select id from master_screen where name ='Team');


-- Endpoints for Team
INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('GET','/program/*/team');

INSERT INTO public.endpoints(
	 http_method, pattern)
	VALUES ('PUT','/program/*/team');



INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Team')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/team') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Team')
	and name= 'CREATE' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/*/team') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Team')
	and name= 'EDIT' )
	,(select id from endpoints where http_method = 'PUT' and pattern ='/program/*/team') ;


INSERT INTO public.master_screen(
	name) values ('Data Flow Input');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Upload','UPLOAD',(select id from master_screen where name ='Data Flow Input');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('POST','/dataflow/upload-excel');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/program/*/partners');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('PUT','/dataflow/reupload-excel/*');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/dataflow/entity-fields');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/dataflow/excel-files');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/dataflow/download');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('POST','/dataflow/validate-and-process/*');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('DELETE','/dataflow/excel-files/*');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/dataflow/excel-files/*/history');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/dataflow/excel-headers/*');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/dataflow/file-types');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/dataflow/entity-types');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('POST','/dataflow/column-mappings');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/value-chain/*/approved-program-list');



INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Data Flow Input')
	and name= 'UPLOAD' )
	,(select id from endpoints where http_method = 'POST' and pattern ='/dataflow/upload-excel') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Data Flow Input')
	and name= 'UPLOAD' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/partners') ;


INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'PUT' AND pattern = '/dataflow/reupload-excel/*');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/dataflow/entity-fields');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/dataflow/excel-files');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/dataflow/download');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'POST' AND pattern = '/dataflow/validate-and-process/*');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'DELETE' AND pattern = '/dataflow/excel-files/*');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/dataflow/excel-files/*/history');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/dataflow/excel-headers/*');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/dataflow/file-types');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/dataflow/entity-types');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'POST' AND pattern = '/dataflow/column-mappings');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/*/approved-program-list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Data Flow Input')
     AND name = 'UPLOAD'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/list');


-- Centre Report

INSERT INTO public.master_screen(
	name) values ('Centre Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'View','VIEW',(select id from master_screen where name ='Centre Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Download','DOWNLOAD',(select id from master_screen where name ='Centre Report');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/centres');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/centres/download');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Centre Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/*/approved-program-list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Centre Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Centre Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/report/centres');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Centre Report')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/partners') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Centre Report')
	and name= 'DOWNLOAD' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/report/centres/download') ;


-- Farmer Report

INSERT INTO public.master_screen(
	name) values ('Farmer Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'View','VIEW',(select id from master_screen where name ='Farmer Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Download','DOWNLOAD',(select id from master_screen where name ='Farmer Report');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/farmers');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/farmers/download');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Farmer Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/*/approved-program-list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Farmer Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Farmer Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/report/farmers');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Farmer Report')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/partners') ;

 INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Farmer Report')
	and name= 'DOWNLOAD' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/report/farmers/download') ;

-- Staff Report

INSERT INTO public.master_screen(
	name) values ('Staff Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'View','VIEW',(select id from master_screen where name ='Staff Report');

 INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Download','DOWNLOAD',(select id from master_screen where name ='Staff Report');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/staffs');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/staffs/download');


INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Staff Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/*/approved-program-list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Staff Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Staff Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/report/staffs');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Staff Report')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/partners') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Staff Report')
	and name= 'DOWNLOAD' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/report/staffs/download') ;


-- Dairy Output Report

INSERT INTO public.master_screen(
	name) values ('Dairy Output Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'View','VIEW',(select id from master_screen where name ='Dairy Output Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Download','DOWNLOAD',(select id from master_screen where name ='Dairy Output Report');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/dairy-output');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/dairy-output/download');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/dairy-field-data/download');



INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Dairy Output Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/*/approved-program-list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Dairy Output Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Dairy Output Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/report/dairy-output');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Dairy Output Report')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/partners') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Dairy Output Report')
	and name= 'DOWNLOAD' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/report/dairy-output/download') ;


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Dairy Output Report')
	and name= 'DOWNLOAD' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/report/dairy-field-data/download') ;

--Cotton Output Report

INSERT INTO public.master_screen(
	name) values ('Cotton Output Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'View','VIEW',(select id from master_screen where name ='Cotton Output Report');

INSERT INTO public.permissions(
    label_name, name,  master_screen_id)
    select 'Download','DOWNLOAD',(select id from master_screen where name ='Cotton Output Report');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/cotton-output');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/cotton-years');

INSERT INTO public.endpoints(
    http_method, pattern)
   VALUES ('GET','/report/cotton-output/download');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Cotton Output Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/*/approved-program-list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Cotton Output Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/value-chain/list');

INSERT INTO public.permission_endpoints (permission_id, endpoint_id)
SELECT
  (SELECT id FROM permissions
   WHERE master_screen_id = (SELECT id FROM master_screen WHERE name = 'Cotton Output Report')
     AND name = 'VIEW'),
  (SELECT id FROM endpoints WHERE http_method = 'GET' AND pattern = '/report/cotton-output');


INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Cotton Output Report')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/program/*/partners') ;

INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Cotton Output Report')
	and name= 'VIEW' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/report/cotton-years') ;

	INSERT INTO public.permission_endpoints(
	permission_id, endpoint_id)
	select (select id from permissions
	where master_screen_id =(select id from master_screen where name ='Cotton Output Report')
	and name= 'DOWNLOAD' )
	,(select id from endpoints where http_method = 'GET' and pattern ='/report/cotton-output/download') ;