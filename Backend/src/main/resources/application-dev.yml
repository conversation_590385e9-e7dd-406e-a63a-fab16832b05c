spring:
  config:
    activate:
      on-profile: dev

  ###############################################################################
  # please do not change above-mentioned  properties.
  ###############################################################################
  datasource:
    url: ${sm://udp-dev-db-url}
    username: ${sm://udp-dev-db-username}
    password: ${sm://udp-dev-db-password}
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${sm://sso-client-id}
            client-secret: ${sm://sso-client-secret}

gcp:
  storage:
    logo:
      root-url: https://storage.googleapis.com
      bucket:
        name: ${sm://udp-dev-logo-bucket}
    file:
      bucket:
        name: ${sm://udp-dev-file-bucket}
user:
  email-domains-allowed: ${sm://udp-dev-email-domains-allowed}