server:
  port: 8080
  servlet:
    context-path: /udp
  forward-headers-strategy: native
  error:
    whitelabel:
      enabled: false

spring:
  profiles:
    default: local
  config:
    import: sm://
  servlet:
    multipart:
      max-file-size: 15MB
      max-request-size: 15MB
  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      connection-timeout: 30000
      pool-name: udp-pool
      max-lifetime: 1800000
      connection-test-query: SELECT 1
  jpa:
    hibernate:
      ddl-auto: none # Automatically update the database schema
    show-sql: false # Show SQL queries in the logs
    properties:
      hibernate:
        format_sql: true # Format SQL queries for better readability

  security:
    oauth2:
      client:
        registration:
          google:
            scope: openid, profile, email
            authorization-grant-type: authorization_code
            redirect-uri: "{baseUrl}/login/oauth2/code/google"
            provider: google
        provider:
          google:
            issuer-uri: https://accounts.google.com
            authorization-uri: https://accounts.google.com/o/oauth2/auth
            token-uri: https://oauth2.googleapis.com/token
            user-info-uri: https://www.googleapis.com/oauth2/v3/userinfo
            jwk-set-uri: https://www.googleapis.com/oauth2/v3/certs
      resourceserver:
        jwt:
          issuer-uri: https://accounts.google.com
  jackson:
    time-zone: Asia/Kolkata

logging:
  level:
    root: INFO
    com:
      zaxxer:
        hikari: ERROR
