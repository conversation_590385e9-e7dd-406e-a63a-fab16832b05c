spring:
  config:
    activate:
      on-profile: prod

  ###############################################################################
  # please do not change above-mentioned  properties.
  ###############################################################################
  datasource:
    url: ${sm://udp-prod-db-url}
    username: ${sm://udp-prod-db-username}
    password: ${sm://udp-prod-db-password}

  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${sm://prod-sso-client-id}
            client-secret: ${sm://prod-sso-client-secret}

gcp:
  storage:
    logo:
      root-url: https://storage.googleapis.com
      bucket:
        name: ${sm://udp-prod-logo-bucket}
    file:
      bucket:
        name: ${sm://udp-prod-file-bucket}
user:
  email-domains-allowed: ${sm://udp-prod-email-domains-allowed}